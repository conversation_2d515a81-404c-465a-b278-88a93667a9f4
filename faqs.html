<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Bajaj Motors</title>
    <link
      href="https://fonts.googleapis.com/css2?family=Roboto&display=swap"
      rel="stylesheet"
    />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Roboto+Flex:opsz,wght@8..144,100..1000&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
    />
    <link rel="stylesheet" href="css/styles.css" />
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: "#3b82f6",
              secondary: "#64748b",
              accent: "#326AD2",
              success: "#10b981",
              failure: "#ef4444",
              "bajaj-red": "#E31937",
              "active-text": "#222222",
            },
            fontFamily: {
              roboto: ["Roboto", "sans-serif"],
              robotoFlex: ["Roboto Flex", "sans-serif"],
            },
          },
        },
      };
    </script>
  </head>

  <body class="bg-gray-50 font-roboto">
    <!-- Header -->
    <header class="fixed top-0 left-0 right-0 z-50">
      <!-- Top Bar -->
      <div class="py-2 top-bar top-bar-transparent">
        <div class="top-bar-content">
          <div class="top-bar-left">
            <img
              src="assets/golcha-logo.png"
              alt="golcha_logo"
              class="top-bar-logo"
            />
            <span class="top-bar-text"
              >GOLCHHA GROUP WITH LEGACY OF 100 YEAR</span
            >
          </div>
          <div class="top-bar-right">
            <img src="./assets/globe.png" alt="globe" class="top-bar-icon" />
            <span class="top-bar-text">International website</span>
          </div>
        </div>
      </div>

      <!-- Main Navbar -->
      <nav class="nav-bg floating-navbar bg-white relative">
        <div class="px-6">
          <div class="flex justify-between items-center h-[110px]">
            <!-- Mobile Menu Button -->
            <button id="mobile-menu-btn" class="lg:hidden p-2">
              <i class="fas fa-bars text-xl text-gray-700"></i>
            </button>

            <!-- Desktop Navigation - Left Side -->
            <div class="hidden lg:flex items-center space-x-8">
              <!-- Motorcycles Dropdown -->
              <div class="relative group">
                <button class="flex items-center space-x-1 hover:text-accent">
                  <span>MOTORCYCLES</span>
                  <i class="fas fa-chevron-down text-xs"></i>
                </button>

                <!-- Mega Dropdown - Fixed Height Container -->
                <div
                  class="absolute top-full left-0 w-screen max-w-5xl bg-white shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50 mega-menu-container"
                >
                  <div class="flex h-full">
                    <!-- Categories Sidebar - Scrollable -->
                    <div class="w-64 bg-gray-100 p-4 categories-sidebar">
                      <div class="space-y-2">
                        <button
                          class="category-btn w-full text-center px-3 py-2 text-sm font-medium hover:text-accent active"
                          data-category="pulsar"
                        >
                          PULSAR
                        </button>
                        <button
                          class="category-btn w-full text-center px-3 py-2 text-sm font-medium hover:text-accent"
                          data-category="dominar"
                        >
                          DOMINAR
                        </button>
                        <button
                          class="category-btn w-full text-center px-3 py-2 text-sm font-medium over:text-accent"
                          data-category="avengers"
                        >
                          AVENGERS
                        </button>
                        <button
                          class="category-btn w-full text-center px-3 py-2 text-sm font-medium hover:text-accent"
                          data-category="discover"
                        >
                          DISCOVER
                        </button>
                        <button
                          class="category-btn w-full text-center px-3 py-2 text-sm font-medium hover:text-accent"
                          data-category="platina"
                        >
                          PLATINA
                        </button>
                      </div>
                    </div>

                    <!-- Models Section - Scrollable -->
                    <div class="mega-menu-scrollable flex-1 flex flex-col">
                      <div class="p-6">
                        <!-- Category Tabs -->
                        <div
                          id="tabs-container"
                          class="flex space-x-6 mb-4 text-sm boder-b-2"
                        >
                          <!-- Tabs will be dynamically generated -->
                        </div>

                        <!-- Models Content -->
                        <div
                          id="models-content"
                          class="model-grid gap-4 models-section"
                        >
                          <!-- Models will be populated by JavaScript -->
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <a href="#" class="hover:text-accent flex items-center"
                >SHOWROOMS</a
              >
              <a href="#" class="hover:text-accent flex items-center"
                >WORKSHOPS</a
              >
              <a href="#" class="hover:text-accent flex items-center hide-1150"
                >EVENTS</a
              >
            </div>

            <!-- Centered Logo -->
            <div
              class="flex-1 flex justify-center items-center min-w-0 flex-shrink-0"
            >
              <a
                href="/index.html"
                class="flex items-center flex-shrink-0 min-w-[120px] justify-center"
              >
                <img class="main-logo px-4" src="assets/logo.png" alt="logo" />
              </a>
            </div>

            <!-- Desktop Navigation - Right Side -->
            <div class="hidden lg:flex items-center space-x-8">
              <a
                href="/book-test-ride.html"
                class="hover:text-accent flex items-center"
                >BOOK TEST RIDE</a
              >
              <a
                href="/about.html"
                class="hover:text-accent flex items-center hide-1150"
                >ABOUT US</a
              >
              <a href="#" class="hover:text-accent flex items-center">NEWS</a>

              <!-- Media Center Dropdown - Desktop -->
              <div class="relative group">
                <button class="flex items-center space-x-1 hover:text-accent">
                  <span>MEDIA CENTER</span>
                  <i class="fas fa-chevron-down text-xs"></i>
                </button>
                <div
                  class="absolute top-full left-0 w-48 bg-white shadow-lg rounded-md opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50 media-dropdown"
                >
                  <div class="py-2">
                    <a
                      href="#"
                      class="block px-4 py-2 hover:bg-gray-100 hover:text-accent"
                      >ABOUT US</a
                    >
                    <a
                      href="/blogs.html"
                      class="block px-4 py-2 hover:bg-gray-100 hover:text-accent"
                      >ANNOUNCEMENTS</a
                    >
                    <a
                      href="/blogs.html"
                      class="block px-4 py-2 hover:bg-gray-100 hover:text-accent"
                      >EVENTS</a
                    >
                    <a
                      href="/blogs.html"
                      class="block px-4 py-2 hover:bg-gray-100 hover:text-accent"
                      >BLOGS</a
                    >
                    <a
                      href="#"
                      class="block px-4 py-2 hover:bg-gray-100 hover:text-accent"
                      >DOWNLOAD CENTER</a
                    >
                    <a
                      href="#"
                      class="block px-4 py-2 hover:bg-gray-100 hover:text-accent"
                      >CONTACT US</a
                    >
                    <a
                      href="faqs.html"
                      class="block px-4 py-2 hover:bg-gray-100 hover:text-accent"
                      >FAQS</a
                    >
                  </div>
                </div>
              </div>
            </div>

            <!-- Mobile BIKES Button -->
            <div class="lg:hidden relative">
              <button
                id="mobile-bikes-btn"
                class="text-gray-700 font-medium flex items-center space-x-1"
              >
                <span>BIKES</span>
                <i class="fas fa-chevron-down text-xs"></i>
              </button>
            </div>
          </div>
        </div>

        <!-- Mobile Menu Overlay - Fixed z-index -->
        <div
          id="mobile-overlay"
          class="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40 hidden"
        ></div>

        <!-- Mobile Menu -->
        <div
          id="mobile-menu"
          class="lg:hidden mobile-nav-section fixed top-0 left-0 w-80 h-screen bg-white z-50 transform -translate-x-full transition-transform duration-300 hidden"
        >
          <div class="p-4 bg-white">
            <button
              id="close-mobile-menu"
              class="absolute top-4 right-4 text-xl text-gray-600"
            >
              <i class="fas fa-times"></i>
            </button>

            <!-- Mobile Menu Items -->
            <div class="mt-8 space-y-4">
              <!-- Mobile Motorcycles Dropdown -->
              <div class="mobile-dropdown">
                <button
                  class="mobile-dropdown-btn flex items-center justify-between w-full text-left font-medium text-gray-700 py-2"
                >
                  <span>BIKES</span>
                  <i class="fas fa-chevron-right text-xs"></i>
                </button>
                <div class="mobile-dropdown-content hidden pl-4 space-y-2 mt-2">
                  <button
                    class="mobile-category-btn block w-full text-left py-2 text-sm"
                    data-category="pulsar"
                  >
                    PULSAR
                  </button>
                  <button
                    class="mobile-category-btn block w-full text-left py-2 text-sm"
                    data-category="dominar"
                  >
                    DOMINAR
                  </button>
                  <button
                    class="mobile-category-btn block w-full text-left py-2 text-sm"
                    data-category="avengers"
                  >
                    AVENGERS
                  </button>
                  <button
                    class="mobile-category-btn block w-full text-left py-2 text-sm"
                    data-category="discover"
                  >
                    DISCOVER
                  </button>
                  <button
                    class="mobile-category-btn block w-full text-left py-2 text-sm"
                    data-category="platina"
                  >
                    PLATINA
                  </button>
                </div>
              </div>

              <a href="#" class="block py-2 font-medium">SHOWROOMS</a>
              <a href="#" class="block py-2 font-medium">WORKSHOPS</a>
              <a href="#" class="block py-2 font-medium">EVENTS</a>
              <a href="#" class="block py-2 font-medium">BOOK TEST RIDE</a>
              <a href="#" class="block py-2 font-medium">ABOUT US</a>
              <a href="#" class="block py-2 font-medium">NEWS</a>

              <!-- Media Center Dropdown - Mobile -->
              <div class="mobile-dropdown">
                <button
                  class="mobile-dropdown-btn flex items-center justify-between w-full text-left font-medium text-gray-700 py-2"
                >
                  <span>MEDIA CENTER</span>
                  <i class="fas fa-chevron-right text-xs"></i>
                </button>
                <div class="mobile-dropdown-content hidden pl-4 space-y-2 mt-2">
                  <a href="#" class="block py-2 text-sm text-gray-600"
                    >ABOUT US</a
                  >
                  <a href="#" class="block py-2 text-sm text-gray-600"
                    >ANNOUNCEMENTS</a
                  >
                  <a href="#" class="block py-2 text-sm text-gray-600"
                    >EVENTS</a
                  >
                  <a href="#" class="block py-2 text-sm text-gray-600">BLOGS</a>
                  <a href="#" class="block py-2 text-sm text-gray-600"
                    >DOWNLOAD CENTER</a
                  >
                  <a href="#" class="block py-2 text-sm text-gray-600"
                    >CONTACT US</a
                  >
                  <a href="#" class="block py-2 text-sm text-gray-600">FAQS</a>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Mobile Category Detail View - Fixed z-index -->
        <div
          id="mobile-category-detail"
          class="lg:hidden mobile-nav-section fixed top-0 left-0 w-80 h-screen bg-white z-50 transform -translate-x-full transition-transform duration-300 hidden"
        >
          <div class="p-4">
            <div class="flex items-center mb-4">
              <button id="back-to-categories" class="mr-3 text-gray-600">
                <i class="fas fa-chevron-left"></i>
              </button>
              <button
                id="close-category-detail"
                class="absolute top-4 right-4 text-xl text-gray-600"
              >
                <i class="fas fa-times"></i>
              </button>
              <span id="category-title" class="font-medium text-gray-800"
                >BIKES</span
              >
            </div>

            <!-- Category Tabs -->
            <div
              id="mobile-tabs-container"
              class="flex space-x-4 mb-4 text-sm border-b pb-2"
            >
              <!-- Tabs will be dynamically generated -->
            </div>

            <!-- Mobile Models List -->
            <div
              id="mobile-models-list"
              class="space-y-3 max-h-[calc(100vh-200px)] overflow-y-auto scrollbar-hide"
            >
              <!-- Models will be populated by JavaScript -->
            </div>
          </div>
        </div>

        <!-- Mobile BIKES Sheet - Same behavior as hamburger menu -->
        <div
          id="mobile-bikes-sheet"
          class="lg:hidden mobile-nav-section fixed top-0 right-0 w-80 h-screen bg-white z-50 transform translate-x-full transition-transform duration-300 hidden"
        >
          <div class="p-4 bg-white relative">
            <div class="flex items-center justify-between mb-4">
              <span
                id="mobile-bikes-header"
                class="font-medium text-gray-800 uppercase"
                >Bikes</span
              >
              <button id="close-bikes-sheet" class="text-xl text-gray-600">
                <i class="fas fa-times"></i>
              </button>
            </div>

            <button
              id="back-to-bikes-categories"
              class="hidden items-center text-sm text-gray-600 mb-4"
            >
              <i class="fas fa-chevron-left mr-2"></i>
              Back
            </button>

            <!-- Categories List -->
            <div id="mobile-bikes-categories-list" class="space-y-1 mt-4">
              <button
                class="mobile-bikes-category-btn w-full flex justify-between items-center py-3 text-left font-medium"
                data-category="pulsar"
              >
                <span>PULSAR</span>
                <i class="fas fa-chevron-right text-xs"></i>
              </button>
              <button
                class="mobile-bikes-category-btn w-full flex justify-between items-center py-3 text-left font-medium"
                data-category="dominar"
              >
                <span>DOMINAR</span>
                <i class="fas fa-chevron-right text-xs"></i>
              </button>
              <button
                class="mobile-bikes-category-btn w-full flex justify-between items-center py-3 text-left font-medium"
                data-category="avengers"
              >
                <span>AVENGERS</span>
                <i class="fas fa-chevron-right text-xs"></i>
              </button>
              <button
                class="mobile-bikes-category-btn w-full flex justify-between items-center py-3 text-left font-medium"
                data-category="discover"
              >
                <span>DISCOVER</span>
                <i class="fas fa-chevron-right text-xs"></i>
              </button>
              <button
                class="mobile-bikes-category-btn w-full flex justify-between items-center py-3 text-left font-medium"
                data-category="platina"
              >
                <span>PLATINA</span>
                <i class="fas fa-chevron-right text-xs"></i>
              </button>
            </div>

            <!-- Category Tabs (hidden initially) -->
            <div
              id="mobile-bikes-tabs-container"
              class="hidden flex gap-x-4 gap-y-2 mb-4 text-sm border-b pb-2"
            >
              <!-- Tabs will be dynamically generated -->
            </div>

            <!-- Mobile Bikes Models List (hidden initially) -->
            <div
              id="mobile-bikes-models-content"
              class="hidden space-y-3 max-h-[calc(100vh-200px)] overflow-y-auto scrollbar-hide"
            >
              <!-- Models will be populated by JavaScript -->
            </div>
          </div>
        </div>
      </nav>
    </header>

    <section
      class="w-full h-[50vh] bg-[url('/assets/blogsBg.jpg')] bg-cover bg-center"
    ></section>
    <div class="max-w-6xl mx-auto p-6">
      <!-- Main Heading -->
      <h1 class="text-3xl font-bold text-center text-gray-900 mb-8">
        Frequently Asked Question
      </h1>

      <div class="flex flex-col lg:flex-row gap-8">
        <!-- Sidebar -->
        <div class="lg:w-1/4">
          <div class="">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">
              Select question type
            </h2>
            <ul class="space-y-3">
              <li>
                <button
                  class="category-btn w-full text-left px-3 py-2 rounded-md hover:bg-gray-100 transition-colors duration-200 text-gray-700"
                  data-category="bajaj"
                >
                  BAJAJ Hub Related
                </button>
              </li>
              <li>
                <button
                  class="category-btn w-full text-left px-3 py-2 rounded-md hover:bg-gray-100 transition-colors duration-200 text-gray-700"
                  data-category="app"
                >
                  App-related Issues
                </button>
              </li>
              <li>
                <button
                  class="category-btn w-full text-left px-3 py-2 rounded-md hover:bg-gray-100 transition-colors duration-200 text-gray-700 bg-gray-100"
                  data-category="ota"
                >
                  OTA (Over-The-Air) Issues
                </button>
              </li>
              <li>
                <button
                  class="category-btn w-full text-left px-3 py-2 rounded-md hover:bg-gray-100 transition-colors duration-200 text-gray-700"
                  data-category="privacy"
                >
                  Privacy-related Issues
                </button>
              </li>
              <li>
                <button
                  class="category-btn w-full text-left px-3 py-2 rounded-md hover:bg-gray-100 transition-colors duration-200 text-gray-700"
                  data-category="positioning"
                >
                  Positioning and Navigation Issues
                </button>
              </li>
              <li>
                <button
                  class="category-btn w-full text-left px-3 py-2 rounded-md hover:bg-gray-100 transition-colors duration-200 text-gray-700"
                  data-category="binding"
                >
                  Binding Issues
                </button>
              </li>
              <li>
                <button
                  class="category-btn w-full text-left px-3 py-2 rounded-md hover:bg-gray-100 transition-colors duration-200 text-gray-700"
                  data-category="vin"
                >
                  VIN-related Issues
                </button>
              </li>
            </ul>
          </div>
          <div class="mt-8 border-t border-gray-200 pt-8">
            <p class="text-gray-700 mb-4">
              <strong>Didn't find the answer to your questions?</strong><br />
              Contact us directly.
            </p>
            <button
              class="bg-primary hover:bg-primary-dark text-white px-6 py-2 rounded-full transition-colors duration-200"
            >
              Contact Info <i class="fa-solid fa-arrow-right"></i>
            </button>
          </div>
        </div>

        <!-- Main Content -->
        <div class="lg:w-3/4">
          <div class="bg-white rounded-lg shadow-sm">
            <!-- FAQ Items -->
            <div class="faq-content" data-category="ota">
              <div class="faq-item border-b border-gray-200">
                <button
                  class="faq-question w-full text-left p-6 flex justify-between items-center hover:bg-gray-50 transition-colors duration-200"
                  data-expanded="true"
                >
                  <span class="text-primary font-medium"
                    >The previous owner still binding the vehicle, how can I fix
                    this issue?</span
                  >
                  <svg
                    class="faq-icon w-5 h-5 text-gray-500 transform rotate-180 transition-transform duration-200"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M19 9l-7 7-7-7"
                    ></path>
                  </svg>
                </button>
                <div class="faq-answer px-6 pb-6">
                  <p class="text-gray-600 leading-relaxed">
                    Please submit an unbinding request through the App (The
                    unbinding request should include the complete VIN number,
                    the user's real name, personal phone number, etc.)
                  </p>
                </div>
              </div>

              <div class="faq-item border-b border-gray-200">
                <button
                  class="faq-question w-full text-left p-6 flex justify-between items-center hover:bg-gray-50 transition-colors duration-200"
                  data-expanded="true"
                >
                  <span class="text-primary font-medium"
                    >Can I bind many vehicles to the APP?</span
                  >
                  <svg
                    class="faq-icon w-5 h-5 text-gray-500 transform rotate-180 transition-transform duration-200"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M19 9l-7 7-7-7"
                    ></path>
                  </svg>
                </button>
                <div class="faq-answer px-6 pb-6">
                  <p class="text-gray-600 leading-relaxed">
                    Yes, you can bind many vehicles and customers can switch
                    vehicles in the garage.
                  </p>
                </div>
              </div>

              <div class="faq-item">
                <button
                  class="faq-question w-full text-left p-6 flex justify-between items-center hover:bg-gray-50 transition-colors duration-200"
                  data-expanded="true"
                >
                  <span class="text-primary font-medium"
                    >Can I unbind the vehicle?</span
                  >
                  <svg
                    class="faq-icon w-5 h-5 text-gray-500 transform rotate-180 transition-transform duration-200"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M19 9l-7 7-7-7"
                    ></path>
                  </svg>
                </button>
                <div class="faq-answer px-6 pb-6">
                  <p class="text-gray-600 leading-relaxed">
                    The customer can unbind the vehicle in the APP Once unbound,
                    the information about the vehicle would be deleted from the
                    server, and can't be retrieved if can't the customer can
                    unbind the vehicle in the APP Once unbound, the information
                    about the vehicle would be deleted from the server and can't
                    be retrieved if can't.
                  </p>
                  <p class="text-gray-600 leading-relaxed mt-2">
                    Question can click the "feedback" in the APP to ask for help
                    from the BAJAJ HUB team.
                  </p>
                </div>
              </div>
            </div>

            <!-- Other category content (hidden by default) -->
            <!-- BAJAJ Hub Related -->
            <div class="faq-content hidden" data-category="bajaj">
              <div class="faq-item border-b border-gray-200">
                <button
                  class="faq-question w-full text-left p-6 flex justify-between items-center hover:bg-gray-50 transition-colors duration-200"
                  data-expanded="false"
                >
                  <span class="text-primary font-medium"
                    >How do I connect my vehicle to BAJAJ Hub?</span
                  >
                  <svg
                    class="faq-icon w-5 h-5 text-gray-500 transition-transform duration-200"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M19 9l-7 7-7-7"
                    ></path>
                  </svg>
                </button>
                <div class="faq-answer px-6 pb-6" style="display: none">
                  <p class="text-gray-600 leading-relaxed">
                    To connect your vehicle to BAJAJ Hub, ensure your vehicle is
                    compatible and follow the setup instructions in the mobile
                    app. You'll need to pair your device via Bluetooth and
                    complete the initial configuration.
                  </p>
                </div>
              </div>

              <div class="faq-item border-b border-gray-200">
                <button
                  class="faq-question w-full text-left p-6 flex justify-between items-center hover:bg-gray-50 transition-colors duration-200"
                  data-expanded="false"
                >
                  <span class="text-primary font-medium"
                    >What features are available in BAJAJ Hub?</span
                  >
                  <svg
                    class="faq-icon w-5 h-5 text-gray-500 transition-transform duration-200"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M19 9l-7 7-7-7"
                    ></path>
                  </svg>
                </button>
                <div class="faq-answer px-6 pb-6" style="display: none">
                  <p class="text-gray-600 leading-relaxed">
                    BAJAJ Hub offers vehicle tracking, remote diagnostics,
                    maintenance alerts, trip history, fuel efficiency
                    monitoring, and security features like anti-theft alerts.
                  </p>
                </div>
              </div>

              <div class="faq-item">
                <button
                  class="faq-question w-full text-left p-6 flex justify-between items-center hover:bg-gray-50 transition-colors duration-200"
                  data-expanded="false"
                >
                  <span class="text-primary font-medium"
                    >Is BAJAJ Hub compatible with all BAJAJ vehicles?</span
                  >
                  <svg
                    class="faq-icon w-5 h-5 text-gray-500 transition-transform duration-200"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M19 9l-7 7-7-7"
                    ></path>
                  </svg>
                </button>
                <div class="faq-answer px-6 pb-6" style="display: none">
                  <p class="text-gray-600 leading-relaxed">
                    BAJAJ Hub is compatible with select BAJAJ vehicle models
                    manufactured after 2020. Please check the compatibility list
                    in the app or contact support for specific model
                    information.
                  </p>
                </div>
              </div>
            </div>

            <!-- App-related Issues -->
            <div class="faq-content hidden" data-category="app">
              <div class="faq-item border-b border-gray-200">
                <button
                  class="faq-question w-full text-left p-6 flex justify-between items-center hover:bg-gray-50 transition-colors duration-200"
                  data-expanded="false"
                >
                  <span class="text-primary font-medium"
                    >The app is not connecting to my vehicle. What should I
                    do?</span
                  >
                  <svg
                    class="faq-icon w-5 h-5 text-gray-500 transition-transform duration-200"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M19 9l-7 7-7-7"
                    ></path>
                  </svg>
                </button>
                <div class="faq-answer px-6 pb-6" style="display: none">
                  <p class="text-gray-600 leading-relaxed">
                    First, ensure Bluetooth is enabled on your phone and the
                    vehicle is in pairing mode. Try restarting both the app and
                    your phone. If the issue persists, clear the app cache or
                    reinstall the application.
                  </p>
                </div>
              </div>

              <div class="faq-item border-b border-gray-200">
                <button
                  class="faq-question w-full text-left p-6 flex justify-between items-center hover:bg-gray-50 transition-colors duration-200"
                  data-expanded="false"
                >
                  <span class="text-primary font-medium"
                    >How do I update the BAJAJ Hub app?</span
                  >
                  <svg
                    class="faq-icon w-5 h-5 text-gray-500 transition-transform duration-200"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M19 9l-7 7-7-7"
                    ></path>
                  </svg>
                </button>
                <div class="faq-answer px-6 pb-6" style="display: none">
                  <p class="text-gray-600 leading-relaxed">
                    You can update the app through Google Play Store (Android)
                    or App Store (iOS). Enable automatic updates to ensure you
                    always have the latest version with new features and bug
                    fixes.
                  </p>
                </div>
              </div>

              <div class="faq-item">
                <button
                  class="faq-question w-full text-left p-6 flex justify-between items-center hover:bg-gray-50 transition-colors duration-200"
                  data-expanded="false"
                >
                  <span class="text-primary font-medium"
                    >Why is the app consuming too much battery?</span
                  >
                  <svg
                    class="faq-icon w-5 h-5 text-gray-500 transition-transform duration-200"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M19 9l-7 7-7-7"
                    ></path>
                  </svg>
                </button>
                <div class="faq-answer px-6 pb-6" style="display: none">
                  <p class="text-gray-600 leading-relaxed">
                    High battery consumption may be due to continuous GPS
                    tracking or background sync. You can optimize battery usage
                    by adjusting location settings and disabling unnecessary
                    background activities in the app settings.
                  </p>
                </div>
              </div>
            </div>

            <!-- Privacy-related Issues -->
            <div class="faq-content hidden" data-category="privacy">
              <div class="faq-item border-b border-gray-200">
                <button
                  class="faq-question w-full text-left p-6 flex justify-between items-center hover:bg-gray-50 transition-colors duration-200"
                  data-expanded="false"
                >
                  <span class="text-primary font-medium"
                    >What data does BAJAJ Hub collect?</span
                  >
                  <svg
                    class="faq-icon w-5 h-5 text-gray-500 transition-transform duration-200"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M19 9l-7 7-7-7"
                    ></path>
                  </svg>
                </button>
                <div class="faq-answer px-6 pb-6" style="display: none">
                  <p class="text-gray-600 leading-relaxed">
                    BAJAJ Hub collects vehicle performance data, location
                    information, usage patterns, and diagnostic information to
                    provide personalized services. All data is encrypted and
                    stored securely according to privacy regulations.
                  </p>
                </div>
              </div>

              <div class="faq-item border-b border-gray-200">
                <button
                  class="faq-question w-full text-left p-6 flex justify-between items-center hover:bg-gray-50 transition-colors duration-200"
                  data-expanded="false"
                >
                  <span class="text-primary font-medium"
                    >Can I delete my personal data?</span
                  >
                  <svg
                    class="faq-icon w-5 h-5 text-gray-500 transition-transform duration-200"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M19 9l-7 7-7-7"
                    ></path>
                  </svg>
                </button>
                <div class="faq-answer px-6 pb-6" style="display: none">
                  <p class="text-gray-600 leading-relaxed">
                    Yes, you can request deletion of your personal data by
                    contacting our support team or using the data deletion
                    option in the app settings. Please note that some data may
                    be retained for legal compliance purposes.
                  </p>
                </div>
              </div>

              <div class="faq-item">
                <button
                  class="faq-question w-full text-left p-6 flex justify-between items-center hover:bg-gray-50 transition-colors duration-200"
                  data-expanded="false"
                >
                  <span class="text-primary font-medium"
                    >Is my location data shared with third parties?</span
                  >
                  <svg
                    class="faq-icon w-5 h-5 text-gray-500 transition-transform duration-200"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M19 9l-7 7-7-7"
                    ></path>
                  </svg>
                </button>
                <div class="faq-answer px-6 pb-6" style="display: none">
                  <p class="text-gray-600 leading-relaxed">
                    Your location data is not shared with third parties without
                    your explicit consent. We only use location data to provide
                    navigation services and emergency assistance features within
                    the app.
                  </p>
                </div>
              </div>
            </div>

            <!-- Positioning and Navigation Issues -->
            <div class="faq-content hidden" data-category="positioning">
              <div class="faq-item border-b border-gray-200">
                <button
                  class="faq-question w-full text-left p-6 flex justify-between items-center hover:bg-gray-50 transition-colors duration-200"
                  data-expanded="false"
                >
                  <span class="text-primary font-medium"
                    >GPS location is not accurate. How can I fix this?</span
                  >
                  <svg
                    class="faq-icon w-5 h-5 text-gray-500 transition-transform duration-200"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M19 9l-7 7-7-7"
                    ></path>
                  </svg>
                </button>
                <div class="faq-answer px-6 pb-6" style="display: none">
                  <p class="text-gray-600 leading-relaxed">
                    Ensure GPS is enabled on your device and you have a clear
                    view of the sky. Poor weather conditions or being in
                    enclosed areas can affect GPS accuracy. Try restarting the
                    app or recalibrating your device's compass.
                  </p>
                </div>
              </div>

              <div class="faq-item border-b border-gray-200">
                <button
                  class="faq-question w-full text-left p-6 flex justify-between items-center hover:bg-gray-50 transition-colors duration-200"
                  data-expanded="false"
                >
                  <span class="text-primary font-medium"
                    >Navigation is not working properly. What should I do?</span
                  >
                  <svg
                    class="faq-icon w-5 h-5 text-gray-500 transition-transform duration-200"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M19 9l-7 7-7-7"
                    ></path>
                  </svg>
                </button>
                <div class="faq-answer px-6 pb-6" style="display: none">
                  <p class="text-gray-600 leading-relaxed">
                    Check your internet connection and ensure location services
                    are enabled. Update the app to the latest version and clear
                    the app cache. If issues persist, try using the navigation
                    feature in an open area with good GPS signal.
                  </p>
                </div>
              </div>

              <div class="faq-item">
                <button
                  class="faq-question w-full text-left p-6 flex justify-between items-center hover:bg-gray-50 transition-colors duration-200"
                  data-expanded="false"
                >
                  <span class="text-primary font-medium"
                    >Can I use navigation offline?</span
                  >
                  <svg
                    class="faq-icon w-5 h-5 text-gray-500 transition-transform duration-200"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M19 9l-7 7-7-7"
                    ></path>
                  </svg>
                </button>
                <div class="faq-answer px-6 pb-6" style="display: none">
                  <p class="text-gray-600 leading-relaxed">
                    Limited offline navigation is available for previously
                    downloaded routes. For full navigation features including
                    real-time traffic updates and route optimization, an
                    internet connection is required.
                  </p>
                </div>
              </div>
            </div>

            <!-- Binding Issues -->
            <div class="faq-content hidden" data-category="binding">
              <div class="faq-item border-b border-gray-200">
                <button
                  class="faq-question w-full text-left p-6 flex justify-between items-center hover:bg-gray-50 transition-colors duration-200"
                  data-expanded="false"
                >
                  <span class="text-primary font-medium"
                    >How do I bind a new vehicle to my account?</span
                  >
                  <svg
                    class="faq-icon w-5 h-5 text-gray-500 transition-transform duration-200"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M19 9l-7 7-7-7"
                    ></path>
                  </svg>
                </button>
                <div class="faq-answer px-6 pb-6" style="display: none">
                  <p class="text-gray-600 leading-relaxed">
                    To bind a new vehicle, go to the "Add Vehicle" section in
                    the app, enter your vehicle's VIN number, and follow the
                    verification process. You may need to provide ownership
                    documents for verification.
                  </p>
                </div>
              </div>

              <div class="faq-item border-b border-gray-200">
                <button
                  class="faq-question w-full text-left p-6 flex justify-between items-center hover:bg-gray-50 transition-colors duration-200"
                  data-expanded="false"
                >
                  <span class="text-primary font-medium"
                    >Vehicle binding failed. What could be the reason?</span
                  >
                  <svg
                    class="faq-icon w-5 h-5 text-gray-500 transition-transform duration-200"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M19 9l-7 7-7-7"
                    ></path>
                  </svg>
                </button>
                <div class="faq-answer px-6 pb-6" style="display: none">
                  <p class="text-gray-600 leading-relaxed">
                    Binding may fail due to incorrect VIN entry, vehicle already
                    bound to another account, or network connectivity issues.
                    Verify the VIN number and ensure you have a stable internet
                    connection during the binding process.
                  </p>
                </div>
              </div>

              <div class="faq-item">
                <button
                  class="faq-question w-full text-left p-6 flex justify-between items-center hover:bg-gray-50 transition-colors duration-200"
                  data-expanded="false"
                >
                  <span class="text-primary font-medium"
                    >How many vehicles can I bind to one account?</span
                  >
                  <svg
                    class="faq-icon w-5 h-5 text-gray-500 transition-transform duration-200"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M19 9l-7 7-7-7"
                    ></path>
                  </svg>
                </button>
                <div class="faq-answer px-6 pb-6" style="display: none">
                  <p class="text-gray-600 leading-relaxed">
                    You can bind up to 5 vehicles to a single BAJAJ Hub account.
                    If you need to bind more vehicles, please contact our
                    support team for assistance with enterprise solutions.
                  </p>
                </div>
              </div>
            </div>

            <!-- VIN-related Issues -->
            <div class="faq-content hidden" data-category="vin">
              <div class="faq-item border-b border-gray-200">
                <button
                  class="faq-question w-full text-left p-6 flex justify-between items-center hover:bg-gray-50 transition-colors duration-200"
                  data-expanded="false"
                >
                  <span class="text-primary font-medium"
                    >Where can I find my vehicle's VIN number?</span
                  >
                  <svg
                    class="faq-icon w-5 h-5 text-gray-500 transition-transform duration-200"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M19 9l-7 7-7-7"
                    ></path>
                  </svg>
                </button>
                <div class="faq-answer px-6 pb-6" style="display: none">
                  <p class="text-gray-600 leading-relaxed">
                    The VIN number can be found on your vehicle registration
                    document, insurance papers, or physically on the vehicle
                    frame. For motorcycles, it's typically located on the
                    steering head or frame near the engine.
                  </p>
                </div>
              </div>

              <div class="faq-item border-b border-gray-200">
                <button
                  class="faq-question w-full text-left p-6 flex justify-between items-center hover:bg-gray-50 transition-colors duration-200"
                  data-expanded="false"
                >
                  <span class="text-primary font-medium"
                    >VIN number is not being accepted. What should I do?</span
                  >
                  <svg
                    class="faq-icon w-5 h-5 text-gray-500 transition-transform duration-200"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M19 9l-7 7-7-7"
                    ></path>
                  </svg>
                </button>
                <div class="faq-answer px-6 pb-6" style="display: none">
                  <p class="text-gray-600 leading-relaxed">
                    Double-check that you've entered the VIN correctly, avoiding
                    confusion between similar characters (0 vs O, 1 vs I).
                    Ensure your vehicle model is compatible with BAJAJ Hub.
                    Contact support if the issue persists.
                  </p>
                </div>
              </div>

              <div class="faq-item">
                <button
                  class="faq-question w-full text-left p-6 flex justify-between items-center hover:bg-gray-50 transition-colors duration-200"
                  data-expanded="false"
                >
                  <span class="text-primary font-medium"
                    >Can I change the VIN number after binding?</span
                  >
                  <svg
                    class="faq-icon w-5 h-5 text-gray-500 transition-transform duration-200"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M19 9l-7 7-7-7"
                    ></path>
                  </svg>
                </button>
                <div class="faq-answer px-6 pb-6" style="display: none">
                  <p class="text-gray-600 leading-relaxed">
                    VIN numbers cannot be changed once a vehicle is bound to
                    your account for security reasons. If you need to correct a
                    VIN, you'll need to unbind the vehicle and bind it again
                    with the correct information.
                  </p>
                </div>
              </div>
            </div>
          </div>

          <!-- Contact Section -->
        </div>
      </div>
    </div>

    <script>
      document.addEventListener("DOMContentLoaded", function () {
        const categoryButtons = document.querySelectorAll(".category-btn");
        const faqContents = document.querySelectorAll(".faq-content");

        categoryButtons.forEach((button) => {
          button.addEventListener("click", function () {
            const category = this.getAttribute("data-category");

            // Remove active class from all buttons
            categoryButtons.forEach((btn) =>
              btn.classList.remove("bg-gray-100")
            );

            // Add active class to clicked button
            this.classList.add("bg-gray-100");

            // Hide all FAQ content
            faqContents.forEach((content) => content.classList.add("hidden"));

            // FIXED: Use specific selector for content sections only
            const targetContent = document.querySelector(
              `.faq-content[data-category="${category}"]`
            );
            if (targetContent) {
              targetContent.classList.remove("hidden");
            }
          });
        });

        // FAQ accordion functionality
        const faqQuestions = document.querySelectorAll(".faq-question");

        faqQuestions.forEach((question) => {
          question.addEventListener("click", function () {
            const faqItem = this.closest(".faq-item");
            const answer = faqItem.querySelector(".faq-answer");
            const icon = this.querySelector(".faq-icon");
            const isExpanded = this.getAttribute("data-expanded") === "true";

            if (isExpanded) {
              // Collapse
              answer.style.display = "none";
              icon.classList.remove("rotate-180");
              this.setAttribute("data-expanded", "false");
            } else {
              // Expand
              answer.style.display = "block";
              icon.classList.add("rotate-180");
              this.setAttribute("data-expanded", "true");
            }
          });
        });
      });
    </script>

    <footer class="bg-gray-50 min-h-screen flex flex-col">
      <!-- Email Signup Section -->
      <div class="flex-1 flex items-center justify-center px-4 py-12">
        <div class="max-w-md w-full">
          <div class="text-center mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-2">
              Sign up for Email
            </h2>
            <p class="text-sm text-gray-500 mb-1">
              Read our
              <a href="#" class="text-blue-500 underline">privacy policy</a>
              to learn about data processing
            </p>
            <p class="text-sm text-gray-500">
              Sign up for BAJAJ latest news and updates
            </p>
          </div>

          <form id="emailForm" class="mb-4">
            <div class="flex gap-2 mb-2">
              <input
                type="email"
                id="email"
                placeholder="YOUR EMAIL ADDRESS"
                class="flex-1 bg-white border border-gray-300 rounded-md px-4 py-3 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
              <button
                type="submit"
                class="bg-blue-500 text-white px-6 py-3 rounded-md text-sm font-medium hover:bg-blue-600"
              >
                SUBSCRIBE NOW
              </button>
            </div>
            <p class="text-xs text-gray-500 text-center">
              This site is protected by reCAPTCHA and the Google
              <a href="#" class="underline">Privacy Policy</a> and
              <a href="#" class="underline">Terms of Service</a> apply.
            </p>
          </form>
        </div>
      </div>

      <!-- Footer Section -->
      <div class="bg-[#0F0F0F] text-white py-12">
        <div class="max-w-6xl mx-auto px-4">
          <div class="text-center mb-8">
            <div class="flex justify-center items-center mb-4">
              <div class="w-12 h-12 mr-3 flex items-center justify-center">
                <img src="/assets/golcha-logo.png" alt="" />
              </div>
              <h3 class="text-xl font-semibold">
                GOLCHHA GROUP WITH LEGACY OF 100 YEAR
              </h3>
            </div>
          </div>

          <!-- Footer Links -->
          <div class="flex justify-center gap-8 text-sm mb-8">
            <a href="#" class="hover:text-gray-300">TERMS OF USE</a>
            <a href="#" class="hover:text-gray-300">PRIVACY INFORMATION</a>
            <a href="#" class="hover:text-gray-300">COOKIES INFORMATION</a>
          </div>

          <!-- Copyright -->
          <div class="text-center text-xs text-gray-400 mb-8">
            <p>
              Copyright © 2025 Bajaj Auto Ltd – A Sole Shareholder Company - A
              Company subject to the Management and Coordination
            </p>
            <p>activities of BAJAJ AUTO. All rights reserved. VAT NO.</p>
          </div>

          <!-- Bottom Section -->
          <div class="flex flex-wrap justify-between items-center gap-4">
            <!-- Bajaj Logo -->
            <div class="h-16 w-32">
              <img src="/assets/logo.png" alt="" />
            </div>

            <!-- Social Media Icons -->
            <div class="flex gap-4 text-2xl">
              <a
                href="#"
                class="text-gray-400 hover:text-white transition-transform transform hover:-translate-y-1"
              >
                <i class="fab fa-instagram"></i>
              </a>
              <a
                href="#"
                class="text-gray-400 hover:text-white transition-transform transform hover:-translate-y-1"
              >
                <i class="fab fa-facebook"></i>
              </a>
              <a
                href="#"
                class="text-gray-400 hover:text-white transition-transform transform hover:-translate-y-1"
              >
                <i class="fab fa-youtube"></i>
              </a>
              <a
                href="#"
                class="text-gray-400 hover:text-white transition-transform transform hover:-translate-y-1"
              >
                <i class="fab fa-tiktok"></i>
              </a>
              <a
                href="#"
                class="text-gray-400 hover:text-white transition-transform transform hover:-translate-y-1"
              >
                <i class="fab fa-twitter"></i>
              </a>
              <a
                href="#"
                class="text-gray-400 hover:text-white transition-transform transform hover:-translate-y-1"
              >
                <i class="fab fa-linkedin"></i>
              </a>
            </div>

            <!-- International Website -->
            <div
              class="flex items-center gap-4 text-lg font-semibold text-white"
            >
              <img class="w-8 h-8" src="/assets/globe.png" alt="" />
              <a href="#" class="hover:text-white">International website</a>
            </div>
          </div>
        </div>
      </div>
    </footer>

    <!-- Scripts -->
    <script>
      document
        .getElementById("emailForm")
        .addEventListener("submit", function (e) {
          e.preventDefault();
          const email = document.getElementById("email").value;
          if (email) {
            alert(
              "Thank you for subscribing! You will receive updates at: " + email
            );
            document.getElementById("email").value = "";
          }
        });
    </script>

    <!-- Navbar Functionality Scripts -->
    <script>
      // Comprehensive motorcycle data with brand-specific categories
      const motorcycleData = {
        pulsar: {
          classic: [
            {
              name: "PULSAR 220F ABS",
              image: "./assets/bikes/pulsar/pulsar_220f_abs.png",
            },
            {
              name: "PULSAR 150 TD",
              image: "/assets/bikes/pulsar/pulsar_150_td.png",
            },
            {
              name: "PULSAR 150",
              image: "/assets/bikes/pulsar/pulsar_150.png",
            },
            {
              name: "PULSAR 125",
              image: "/assets/bikes/pulsar/pulsar_125.png",
            },
          ],
          ns: [
            {
              name: "PULSAR NS400Z",
              image: "/assets/bikes/pulsar/ns_400z.png",
            },
            {
              name: "PULSAR NS 200 ABS FI",
              image: "/assets/bikes/pulsar/ns_200_abs_fi.png",
            },
            {
              name: "PULSAR NS 200 ABS",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=NS200",
            },
            {
              name: "PULSAR NS 200",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=NS200",
            },
            {
              name: "PULSAR NS 160 ABS",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=NS160",
            },
            {
              name: "PULSAR NS160 FI DUAL ABS BS6",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=NS160BS6",
            },
            {
              name: "PULSAR NS 125 BS6",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=NS125BS6",
            },
            {
              name: "PULSAR NS 125 FI BS6",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=NS125FIB",
            },
          ],
          n: [
            {
              name: "PULSAR N250",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=N250",
            },
            {
              name: "PULSAR N160",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=N160",
            },
          ],
        },
        dominar: {
          classic: [
            {
              name: "DOMINAR 400",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=D400",
            },
            {
              name: "DOMINAR 250",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=D250",
            },
          ],
        },
        avengers: {
          cruiser: [
            {
              name: "AVENGER CRUISE 220",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=AV220",
            },
            {
              name: "AVENGER STREET 160",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=AV160",
            },
          ],
        },
        discover: {
          commuter: [
            {
              name: "DISCOVER 125",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=D125",
            },
            {
              name: "DISCOVER 110",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=D110",
            },
          ],
        },
        platina: {
          commuter: [
            {
              name: "PLATINA 110",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=PL110",
            },
            {
              name: "PLATINA 100",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=PL100",
            },
          ],
        },
      };

      let currentCategory = "pulsar";
      let currentTab = "all";

      // DOM elements
      const mobileOverlay = document.getElementById("mobile-overlay");
      const mobileMenu = document.getElementById("mobile-menu");
      const mobileCategoryDetail = document.getElementById(
        "mobile-category-detail"
      );
      const mobileBikesSheet = document.getElementById("mobile-bikes-sheet");
      const mobileMenuBtn = document.getElementById("mobile-menu-btn");
      const mobileBikesBtn = document.getElementById("mobile-bikes-btn");
      const closeMobileMenu = document.getElementById("close-mobile-menu");
      const closeBikesSheet = document.getElementById("close-bikes-sheet");
      const backToCategories = document.getElementById("back-to-categories");
      const closeCategoryDetail = document.getElementById(
        "close-category-detail"
      );

      // Mobile Bikes Sheet Elements
      const mobileBikesHeader = document.getElementById("mobile-bikes-header");
      const backToBikesCategories = document.getElementById(
        "back-to-bikes-categories"
      );
      const mobileBikesCategoriesList = document.getElementById(
        "mobile-bikes-categories-list"
      );
      const mobileBikesTabsContainer = document.getElementById(
        "mobile-bikes-tabs-container"
      );
      const mobileBikesModelsContent = document.getElementById(
        "mobile-bikes-models-content"
      );

      // Function to format sub-category names
      function formatSubCategoryName(name) {
        if (name === "ns") return "NS";
        if (name === "n") return "N";
        if (name === "classic") return "Classic";
        if (name === "cruiser") return "Cruiser";
        if (name === "commuter") return "Commuter";
        return name.charAt(0).toUpperCase() + name.slice(1);
      }

      // Function to render tabs dynamically
      function renderTabs(category, isMobile = false, customContainer = null) {
        const tabsContainer = customContainer
          ? document.getElementById(customContainer)
          : isMobile
          ? document.getElementById("mobile-tabs-container")
          : document.getElementById("tabs-container");

        tabsContainer.innerHTML = "";

        // Get available tabs for this category
        const availableTabs = Object.keys(motorcycleData[category]).filter(
          (tab) => motorcycleData[category][tab].length > 0
        );

        // Always show "All" tab
        const allTab = document.createElement("button");
        allTab.className = `tab-btn ${
          currentTab === "all"
            ? "border-b-2 border-active-text font-medium active"
            : "text-active-text font-medium"
        } pb-1`;
        allTab.dataset.tab = "all";
        allTab.textContent = "All";
        tabsContainer.appendChild(allTab);

        // Create tabs for each available category
        availableTabs.forEach((tabName) => {
          const tab = document.createElement("button");
          tab.className = `tab-btn ${
            currentTab === tabName
              ? "border-b-2 border-active-text active font-medium"
              : "text-active-text font-medium"
          } pb-1`;
          tab.dataset.tab = tabName;
          tab.textContent = formatSubCategoryName(tabName);
          tabsContainer.appendChild(tab);
        });

        // Add event listeners to new tabs
        addTabEventListeners(isMobile);
      }

      // Function to render models
      function renderModels(
        category,
        tab,
        isMobile = false,
        customContainer = null
      ) {
        const container = customContainer
          ? document.getElementById(customContainer)
          : isMobile
          ? document.getElementById("mobile-models-list")
          : document.getElementById("models-content");

        let models = [];

        if (tab === "all") {
          // Create container HTML with category headings
          let containerHTML = "";

          // Get all sub-categories for this category
          const subCategories = Object.keys(motorcycleData[category]);

          // Iterate through each sub-category
          for (const subCategory of subCategories) {
            const subCategoryModels =
              motorcycleData[category][subCategory] || [];
            if (subCategoryModels.length === 0) continue;

            // Add category heading
            if (isMobile) {
              containerHTML += `
                            <div class="mt-4 mb-2 pl-3">
                                <div class="text-sm font-semibold text-accent border-b border-gray-200 pb-1">
                                    ${formatSubCategoryName(subCategory)}
                                </div>
                            </div>
                        `;
            } else {
              containerHTML += `
                            <div class="category-heading flex items-center gap-2 p-2">
                                <div class="w-1 h-6 bg-accent rounded-r-sm"></div>
                                    <span class="font-semibold text-black text-base">${formatSubCategoryName(
                                      subCategory
                                    )}</span>
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        class="w-4 h-4 text-black ml-auto"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                        stroke-width="2"
                                    >
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7" />
                                    </svg>
                            </div>
                        `;
            }

            // Add models for this sub-category
            subCategoryModels.forEach((model) => {
              if (isMobile) {
                containerHTML += `
                                <div class="mobile-model-item flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg cursor-pointer">
                                    <img src="${model.image}" alt="${model.name}" class="w-16 h-10 object-cover rounded">
                                    <div class="flex-1">
                                        <div class="text-sm font-medium text-gray-800">${model.name}</div>
                                    </div>
                                    <i class="fas fa-chevron-right text-gray-400"></i>
                                </div>
                            `;
              } else {
                containerHTML += `
                                <div class="model-item text-center p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors">
                                    <img src="${model.image}" alt="${model.name}" class="mx-auto mb-2 w-24 h-16 object-cover rounded">
                                    <p class="text-xs font-medium text-gray-800 mb-1">${model.name}</p>
                                </div>
                            `;
              }
            });
          }

          container.innerHTML = containerHTML;
        } else {
          // For specific tabs, just show models without headings
          models = motorcycleData[category][tab] || [];

          if (isMobile) {
            container.innerHTML = models
              .map(
                (model) => `
                        <div class="mobile-model-item flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg cursor-pointer">
                            <img src="${model.image}" alt="${model.name}" class="w-16 h-10 object-cover rounded">
                            <div class="flex-1">
                                <div class="text-sm font-medium text-gray-800">${model.name}</div>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>
                    `
              )
              .join("");
          } else {
            container.innerHTML = models
              .map(
                (model) => `
                        <div class="model-item text-center p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors">
                            <img src="${model.image}" alt="${model.name}" class="mx-auto mb-2 w-24 h-16 object-cover rounded">
                            <p class="text-xs font-medium text-gray-800 mb-1">${model.name}</p>
                        </div>
                    `
              )
              .join("");
          }

          // Show message if no models found
          if (models.length === 0) {
            container.innerHTML = `
                        <div class="${
                          isMobile
                            ? "text-center py-8 text-gray-500"
                            : "col-span-full text-center py-8 text-gray-500"
                        }">
                            No models available in this category
                        </div>
                    `;
          }
        }
      }

      // Initialize with default category and tab
      renderTabs(currentCategory);
      renderModels(currentCategory, currentTab);

      // Add tab event listeners
      function addTabEventListeners(isMobile = false) {
        const selector = isMobile
          ? "#mobile-tabs-container .tab-btn"
          : "#tabs-container .tab-btn";
        const tabs = document.querySelectorAll(selector);

        tabs.forEach((tab) => {
          tab.addEventListener("click", () => {
            const tabName = tab.dataset.tab;
            currentTab = tabName;

            // Update active tab styling
            tabs.forEach((t) => {
              t.classList.remove(
                "text-accent",
                "border-b-2",
                "border-accent",
                "active"
              );
              t.classList.add("text-gray-500");
            });
            tab.classList.remove("text-gray-500");
            tab.classList.add(
              "text-accent",
              "border-b-2",
              "border-accent",
              "active"
            );

            renderModels(currentCategory, tabName, isMobile);
          });
        });
      }

      // Mobile menu functionality
      // Open mobile menu
      mobileMenuBtn.addEventListener("click", () => {
        mobileMenu.classList.remove("hidden");
        mobileMenu.classList.remove("-translate-x-full");
        mobileOverlay.classList.remove("hidden");
        document.body.style.overflow = "hidden";
      });

      // Close mobile menu
      function closeMobileMenuFunc() {
        mobileMenu.classList.add("-translate-x-full");
        // Add a small delay before hiding to allow the slide animation to complete
        setTimeout(() => {
          mobileMenu.classList.add("hidden");
        }, 300);
        mobileCategoryDetail.classList.add("-translate-x-full");
        mobileBikesSheet.classList.add("translate-x-full");
        mobileOverlay.classList.add("hidden");
        document.body.style.overflow = "auto";
      }

      closeMobileMenu.addEventListener("click", closeMobileMenuFunc);
      mobileOverlay.addEventListener("click", closeMobileMenuFunc);

      // Handle window resize to ensure mobile menu is hidden on desktop
      window.addEventListener("resize", () => {
        if (window.innerWidth >= 1024) {
          // Close mobile menu when switching to desktop view
          closeMobileMenuFunc();
        }
      });

      // Mobile dropdown functionality
      document.querySelectorAll(".mobile-dropdown-btn").forEach((btn) => {
        btn.addEventListener("click", () => {
          const content = btn.nextElementSibling;
          const icon = btn.querySelector("i");

          if (content.classList.contains("hidden")) {
            content.classList.remove("hidden");
            icon.classList.remove("fa-chevron-right");
            icon.classList.add("fa-chevron-down");
          } else {
            content.classList.add("hidden");
            icon.classList.remove("fa-chevron-down");
            icon.classList.add("fa-chevron-right");
          }
        });
      });

      // Mobile category selection (show panel)
      document.querySelectorAll(".mobile-category-btn").forEach((btn) => {
        btn.addEventListener("click", () => {
          const category = btn.dataset.category;
          currentCategory = category;
          currentTab = "all";

          // Update category title
          document.getElementById(
            "category-title"
          ).textContent = `BIKES / ${category.toUpperCase()}`;

          // Render tabs and models for mobile
          renderTabs(category, true);
          renderModels(category, "all", true);

          // Hide overlay when showing model list
          mobileOverlay.classList.add("hidden");

          // Transition to model view
          mobileMenu.classList.add("-translate-x-full");
          setTimeout(() => {
            mobileCategoryDetail.classList.remove("hidden");
            mobileCategoryDetail.classList.remove("-translate-x-full");
          }, 50);
        });
      });

      // Back to categories (hide panel)
      backToCategories.addEventListener("click", () => {
        mobileCategoryDetail.classList.add("-translate-x-full");
        setTimeout(() => {
          mobileCategoryDetail.classList.add("hidden");
          mobileMenu.classList.remove("-translate-x-full");
          mobileOverlay.classList.remove("hidden");
        }, 300); // match transition duration
      });

      // Close category detail (hide panel)
      closeCategoryDetail.addEventListener("click", () => {
        mobileCategoryDetail.classList.add("-translate-x-full");
        setTimeout(() => {
          mobileCategoryDetail.classList.add("hidden");
          mobileMenu.classList.remove("-translate-x-full");
          mobileOverlay.classList.add("hidden");
          document.body.style.overflow = "auto";
        }, 300);
      });

      // Desktop category switching
      document.querySelectorAll(".category-btn").forEach((btn) => {
        btn.addEventListener("click", () => {
          const category = btn.dataset.category;
          currentCategory = category;
          currentTab = "all";

          // Remove active class from all buttons
          document.querySelectorAll(".category-btn").forEach((b) => {
            b.classList.remove("active");
          });

          // Add active class to clicked button
          btn.classList.add("active");

          // Render new tabs and models
          renderTabs(category);
          renderModels(category, "all");
        });
      });

      // Add click handlers for model items
      document.addEventListener("click", (e) => {
        if (
          e.target.closest(".model-item") ||
          e.target.closest(".mobile-model-item")
        ) {
          const modelElement = e.target.closest(
            ".model-item, .mobile-model-item"
          );
          const modelName =
            modelElement.querySelector("p, .text-sm").textContent;
          alert(
            `You selected: ${modelName}\n\nThis would typically navigate to the model details page.`
          );
        }
      });

      // Initialize tabs with event listeners
      addTabEventListeners();

      // Mobile BIKES sheet functionality
      // Function to reset bikes sheet to initial state (category list)
      const resetBikesSheet = () => {
        mobileBikesHeader.textContent = "BIKES";
        mobileBikesCategoriesList.classList.remove("hidden");
        backToBikesCategories.classList.add("hidden");
        mobileBikesTabsContainer.classList.add("hidden");
        mobileBikesModelsContent.classList.add("hidden");
        mobileBikesSheet.classList.add("hidden");
      };

      // Open mobile bikes sheet
      mobileBikesBtn.addEventListener("click", () => {
        mobileBikesSheet.classList.remove("hidden", "translate-x-full");
        mobileOverlay.classList.remove("hidden");
        document.body.style.overflow = "hidden";
      });

      // Close mobile bikes sheet
      const closeBikesSheetFunc = () => {
        mobileBikesSheet.classList.add("translate-x-full");
        if (
          document
            .getElementById("mobile-menu")
            .classList.contains("-translate-x-full")
        ) {
          mobileOverlay.classList.add("hidden");
          document.body.style.overflow = "auto";
        }
        setTimeout(resetBikesSheet, 300); // Reset after transition
      };
      closeBikesSheet.addEventListener("click", closeBikesSheetFunc);

      // Mobile bikes category selection from sheet
      document.querySelectorAll(".mobile-bikes-category-btn").forEach((btn) => {
        btn.addEventListener("click", () => {
          const category = btn.dataset.category;
          currentCategory = category;
          currentTab = "all";

          // Update UI for model view
          mobileBikesHeader.textContent = category.toUpperCase();
          mobileBikesCategoriesList.classList.add("hidden");
          backToBikesCategories.classList.remove("hidden");
          mobileBikesTabsContainer.classList.remove("hidden");
          mobileBikesModelsContent.classList.remove("hidden");

          // Render content for selected category
          renderTabs(category, true, "mobile-bikes-tabs-container");
          renderModels(category, "all", true, "mobile-bikes-models-content");
        });
      });

      // Back to bikes categories
      backToBikesCategories.addEventListener("click", resetBikesSheet);

      // Mobile bikes tab switching
      document.addEventListener("click", (e) => {
        if (e.target.closest("#mobile-bikes-tabs-container .tab-btn")) {
          const tab = e.target.closest("#mobile-bikes-tabs-container .tab-btn");
          const tabName = tab.dataset.tab;
          currentTab = tabName;

          // Update active tab styling
          document
            .querySelectorAll("#mobile-bikes-tabs-container .tab-btn")
            .forEach((t) => {
              t.classList.remove("border-b-2", "border-accent", "active");
              t.classList.add("text-gray-500");
            });
          tab.classList.remove("text-gray-500");
          tab.classList.add(
            "text-accent",
            "border-b-2",
            "border-accent",
            "active"
          );

          renderModels(
            currentCategory,
            tabName,
            true,
            "mobile-bikes-models-content"
          );
        }
      });
    </script>
  </body>
</html>
