<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Bajaj Locations Map, Nepal</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <!-- Leaflet CSS -->
  <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
  <style>
    #map {
      height: 100vh;
      width: 100%;
      position: relative;
      z-index: 1;
    }
    
    /* Navbar Styles */
    .navbar {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      background: rgba(255, 255, 255, 0.95);
      z-index: 1000;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    
    /* Dropdown Styles */
    .dropdown-content {
      display: none;
      position: absolute;
      background: white;
      min-width: 200px;
      box-shadow: 0 8px 16px rgba(0,0,0,0.1);
      z-index: 1001;
      opacity: 0;
      transition: all 0.3s ease;
    }
    
    .dropdown:hover .dropdown-content {
      display: block;
      opacity: 1;
    }
    
    /* Mobile styles */
    @media (max-width: 768px) {
      .navbar-links {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        padding: 1rem;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
      }
      
      .navbar-links.active {
        display: block;
      }
      
      .dropdown-content {
        position: static;
        display: none;
        box-shadow: none;
        padding-left: 1rem;
      }
      
      .dropdown.active .dropdown-content {
        display: block;
      }
    }
  </style>
</head>
<body class="bg-gray-100">
  <!-- Navbar -->
  <nav class="navbar">
    <!-- Top Bar -->
    <div class="bg-blue-800 text-white py-2 px-4">
      <div class="container mx-auto flex justify-between items-center">
        <div class="flex items-center space-x-2">
          <img src="https://via.placeholder.com/30x30" alt="logo" class="h-6">
          <span class="text-sm">GOLCHHA GROUP WITH LEGACY OF 100 YEAR</span>
        </div>
        <div class="flex items-center space-x-2">
          <svg class="w-4 h-4" viewBox="0 0 23 18" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M6.89761 15.1618C8.28247 16.3099 10.0607 17 12.0001 17C16.4184 17 20.0001 13.4183 20.0001 9C20.0001 8.43095 19.9407 7.87578 19.8278 7.34036M6.89761 15.1618C5.12756 13.6944 4.00014 11.4789 4.00014 9C4.00014 4.58172 7.58186 1 12.0001 1C15.8494 1 19.0637 3.71853 19.8278 7.34036M6.89761 15.1618C8.85314 14.7147 11.1796 13.7828 13.526 12.4281C16.2564 10.8517 18.4773 9.01248 19.8278 7.34036M6.89761 15.1618C4.46844 15.7171 2.61159 15.5243 1.99965 14.4644C1.36934 13.3726 2.19631 11.5969 3.99999 9.70898M19.8278 7.34036C21.0796 5.79041 21.5836 4.38405 21.0522 3.46374C20.5134 2.53051 19.0095 2.26939 16.9997 2.59929" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          <span class="text-sm">International website</span>
        </div>
      </div>
    </div>
    
    <!-- Main Navigation -->
    <div class="container mx-auto px-4 py-2">
      <div class="flex justify-between items-center">
        <!-- Mobile menu button -->
        <button class="lg:hidden p-2" id="mobile-menu-button">
          <i class="fas fa-bars text-gray-700"></i>
        </button>
        
        <!-- Logo -->
        <img src="https://via.placeholder.com/120x50" alt="logo" class="h-12">
        
        <!-- Desktop Navigation -->
        <div class="hidden lg:flex space-x-6">
          <div class="dropdown relative">
            <button class="flex items-center space-x-1 text-gray-700 hover:text-blue-600">
              <span>MOTORCYCLES</span>
              <i class="fas fa-chevron-down text-xs"></i>
            </button>
            <div class="dropdown-content mt-2 rounded-lg">
              <a href="#" class="block px-4 py-2 hover:bg-gray-100">All Models</a>
              <a href="#" class="block px-4 py-2 hover:bg-gray-100">Pulsar Series</a>
              <a href="#" class="block px-4 py-2 hover:bg-gray-100">Dominar Series</a>
            </div>
          </div>
          
          <a href="#" class="text-gray-700 hover:text-blue-600">SHOWROOMS</a>
          <a href="#" class="text-gray-700 hover:text-blue-600">WORKSHOPS</a>
          <a href="#" class="text-gray-700 hover:text-blue-600">EVENTS</a>
          
          <div class="dropdown relative">
            <button class="flex items-center space-x-1 text-gray-700 hover:text-blue-600">
              <span>MEDIA CENTER</span>
              <i class="fas fa-chevron-down text-xs"></i>
            </button>
            <div class="dropdown-content mt-2 rounded-lg">
              <a href="#" class="block px-4 py-2 hover:bg-gray-100">ABOUT US</a>
              <a href="#" class="block px-4 py-2 hover:bg-gray-100">ANNOUNCEMENTS</a>
              <a href="#" class="block px-4 py-2 hover:bg-gray-100">EVENTS</a>
              <a href="#" class="block px-4 py-2 hover:bg-gray-100">BLOGS</a>
            </div>
          </div>
        </div>
        
        <!-- Mobile Navigation (hidden by default) -->
        <div class="navbar-links lg:hidden" id="mobile-nav">
          <div class="flex flex-col space-y-2 py-4">
            <div class="dropdown">
              <button class="flex justify-between items-center w-full px-4 py-2 text-left text-gray-700">
                <span>MOTORCYCLES</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <div class="dropdown-content">
                <a href="#" class="block px-4 py-2 hover:bg-gray-100">All Models</a>
                <a href="#" class="block px-4 py-2 hover:bg-gray-100">Pulsar Series</a>
                <a href="#" class="block px-4 py-2 hover:bg-gray-100">Dominar Series</a>
              </div>
            </div>
            
            <a href="#" class="px-4 py-2 text-gray-700">SHOWROOMS</a>
            <a href="#" class="px-4 py-2 text-gray-700">WORKSHOPS</a>
            <a href="#" class="px-4 py-2 text-gray-700">EVENTS</a>
            
            <div class="dropdown">
              <button class="flex justify-between items-center w-full px-4 py-2 text-left text-gray-700">
                <span>MEDIA CENTER</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              <div class="dropdown-content">
                <a href="#" class="block px-4 py-2 hover:bg-gray-100">ABOUT US</a>
                <a href="#" class="block px-4 py-2 hover:bg-gray-100">ANNOUNCEMENTS</a>
                <a href="#" class="block px-4 py-2 hover:bg-gray-100">EVENTS</a>
                <a href="#" class="block px-4 py-2 hover:bg-gray-100">BLOGS</a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </nav>

  <!-- Map Container -->
  <div id="map"></div>

  <!-- Footer -->
  <footer class="bg-gray-900 text-white py-8">
    <div class="container mx-auto px-4">
      <div class="text-center mb-6">
        <div class="flex justify-center items-center mb-2">
          <img src="https://via.placeholder.com/30x30" alt="logo" class="h-6 mr-2">
          <h3 class="text-lg">GOLCHHA GROUP WITH LEGACY OF 100 YEAR</h3>
        </div>
      </div>
      
      <div class="flex justify-center space-x-6 mb-6">
        <a href="#" class="hover:text-gray-300">TERMS OF USE</a>
        <a href="#" class="hover:text-gray-300">PRIVACY</a>
        <a href="#" class="hover:text-gray-300">COOKIES</a>
      </div>
      
      <div class="text-center text-sm text-gray-400">
        <p>Copyright © 2025 Bajaj Auto Ltd. All rights reserved.</p>
      </div>
    </div>
  </footer>

  <!-- Leaflet JS -->
  <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
  
  <script>
    // Initialize the map centered on Nepal
    const map = L.map('map').setView([28.3949, 84.1240], 7);

    // Add OpenStreetMap tiles
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(map);

    // Define locations in Nepal (latitude, longitude, name)
    const locations = [
      { lat: 27.7172, lng: 85.3240, name: "Kathmandu" },
      { lat: 27.6728, lng: 85.4298, name: "Bhaktapur" },
      { lat: 27.6786, lng: 85.3157, name: "Patan" },
      { lat: 28.2026, lng: 83.9856, name: "Pokhara" },
      { lat: 26.4525, lng: 87.2718, name: "Biratnagar" },
      { lat: 28.7050, lng: 80.5664, name: "Dhangadi" },
      { lat: 27.5892, lng: 84.5066, name: "Chitwan (Bharatpur)" },
      { lat: 28.8417, lng: 80.2458, name: "Kanchanpur (Mahendranagar)" }
    ];

    // Add markers for each location
    locations.forEach(loc => {
      L.marker([loc.lat, loc.lng])
        .addTo(map)
        .bindPopup(`<b>${loc.name}</b>`);
    });

    // Fit bounds to include all markers
    const bounds = L.latLngBounds(locations.map(loc => [loc.lat, loc.lng]));
    map.fitBounds(bounds, { padding: [50, 50] });

    // Navbar functionality
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileNav = document.getElementById('mobile-nav');
    
    // Toggle mobile menu
    mobileMenuButton.addEventListener('click', () => {
      mobileNav.classList.toggle('active');
    });
    
    // Close mobile menu when clicking outside
    document.addEventListener('click', (e) => {
      if (!e.target.closest('.navbar') && !e.target.closest('#mobile-menu-button')) {
        mobileNav.classList.remove('active');
      }
    });
    
    // Mobile dropdown functionality
    document.querySelectorAll('.dropdown button').forEach(button => {
      button.addEventListener('click', (e) => {
        if (window.innerWidth <= 768) {
          e.preventDefault();
          const dropdown = button.closest('.dropdown');
          dropdown.classList.toggle('active');
          
          // Close other dropdowns
          document.querySelectorAll('.dropdown').forEach(other => {
            if (other !== dropdown) {
              other.classList.remove('active');
            }
          });
        }
      });
    });
    
    // Close dropdowns when resizing to desktop
    window.addEventListener('resize', () => {
      if (window.innerWidth > 768) {
        mobileNav.classList.remove('active');
        document.querySelectorAll('.dropdown').forEach(dropdown => {
          dropdown.classList.remove('active');
        });
      }
    });
  </script>
</body>
</html>