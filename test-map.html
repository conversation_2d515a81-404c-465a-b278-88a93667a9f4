<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Nepal Map with Dropdown Navbar</title>
  <!-- Leaflet CSS & JS -->
  <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
  <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <style>
    body, html {
      margin: 0;
      padding: 0;
      height: 100%;
      font-family: Arial, sans-serif;
    }
    #map {
      width: 100%;
      height: 100vh;
    }
    /* Navbar Styles */
    .navbar {
      position: absolute;
      top: 10px;
      left: 10px;
      right: 10px;
      background: rgba(255, 255, 255, 0.95);
      border-radius: 5px;
      padding: 10px 15px;
      box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
      z-index: 1000;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .navbar-brand {
      font-weight: bold;
      font-size: 1.2em;
      color: #2c3e50;
      text-decoration: none;
    }
    .navbar-links {
      display: flex;
      gap: 20px;
      position: relative;
    }
    .nav-item {
      position: relative;
    }
    .nav-link {
      color: #2c3e50;
      text-decoration: none;
      padding: 8px 12px;
      border-radius: 4px;
      transition: all 0.3s;
      display: flex;
      align-items: center;
      gap: 5px;
    }
    .nav-link:hover {
      background: rgba(0, 0, 0, 0.05);
    }
    /* Dropdown Styles */
    .dropdown {
      position: relative;
      display: inline-block;
    }
    .dropdown-content {
      display: none;
      position: absolute;
      top: 100%;
      left: 0;
      background: white;
      min-width: 180px;
      box-shadow: 0 8px 16px rgba(0,0,0,0.1);
      border-radius: 4px;
      z-index: 1;
      opacity: 0;
      transform: translateY(-10px);
      transition: all 0.3s ease;
    }
    .dropdown:hover .dropdown-content {
      display: block;
      opacity: 1;
      transform: translateY(0);
    }
    .dropdown-item {
      padding: 10px 15px;
      text-decoration: none;
      display: block;
      color: #333;
      transition: background 0.2s;
    }
    .dropdown-item:hover {
      background: #f5f5f5;
    }
    .dropdown-item i {
      margin-right: 8px;
      width: 16px;
      text-align: center;
    }
    /* Mobile Menu */
    .menu-toggle {
      display: none;
      cursor: pointer;
      font-size: 1.2em;
    }
    @media (max-width: 768px) {
      .navbar {
        flex-direction: column;
        align-items: flex-start;
      }
      .navbar-links {
        display: none;
        width: 100%;
        flex-direction: column;
        margin-top: 10px;
      }
      .navbar-links.active {
        display: flex;
      }
      .dropdown-content {
        position: static;
        box-shadow: none;
        display: none;
        opacity: 1;
        transform: none;
      }
      .dropdown:hover .dropdown-content {
        display: none;
      }
      .dropdown.active .dropdown-content {
        display: block;
        margin-left: 15px;
      }
      .menu-toggle {
        display: block;
        position: absolute;
        right: 15px;
        top: 15px;
      }
    }
  </style>
</head>
<body>
  <!-- Floating Navbar with Dropdown -->
  <nav class="navbar">
    <a href="#" class="navbar-brand">Nepal Map</a>
    <div class="menu-toggle" id="mobile-menu">
      <i class="fas fa-bars"></i>
    </div>
    <div class="navbar-links" id="navbar-links">
      <div class="nav-item">
        <a href="#" class="nav-link"><i class="fas fa-home"></i> Home</a>
      </div>
      <div class="nav-item dropdown">
        <a href="#" class="nav-link"><i class="fas fa-info-circle"></i> About Us <i class="fas fa-caret-down"></i></a>
        <div class="dropdown-content">
          <a href="#" class="dropdown-item"><i class="fas fa-users"></i> Our Teams</a>
          <a href="#" class="dropdown-item"><i class="fas fa-road"></i> Our Journey</a>
          <a href="#" class="dropdown-item"><i class="fas fa-map-marker-alt"></i> Our Location</a>
        </div>
      </div>
      <div class="nav-item">
        <a href="#" class="nav-link"><i class="fas fa-envelope"></i> Contact Us</a>
      </div>
      <div class="nav-item">
        <a href="#" class="nav-link"><i class="fas fa-blog"></i> Blogs</a>
      </div>
    </div>
  </nav>

  <!-- Map Container -->
  <div id="map"></div>

  <script>
    // Initialize the map centered on Nepal
    const map = L.map('map').setView([28.3949, 84.1240], 7);

    // Add OpenStreetMap tiles
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(map);

    // Define locations in Nepal
    const locations = [
      { lat: 27.7172, lng: 85.3240, name: "Kathmandu" },
      { lat: 27.6728, lng: 85.4298, name: "Bhaktapur" },
      { lat: 27.6786, lng: 85.3157, name: "Patan" },
      { lat: 28.2026, lng: 83.9856, name: "Pokhara" },
      { lat: 26.4525, lng: 87.2718, name: "Biratnagar" },
      { lat: 28.7050, lng: 80.5664, name: "Dhangadi" },
      { lat: 27.5892, lng: 84.5066, name: "Chitwan (Bharatpur)" },
      { lat: 28.8417, lng: 80.2458, name: "Kanchanpur (Mahendranagar)" }
    ];

    // Add markers
    locations.forEach(loc => {
      L.marker([loc.lat, loc.lng])
        .addTo(map)
        .bindPopup(`<b>${loc.name}</b>`);
    });

    // Fit bounds to include all markers
    const bounds = L.latLngBounds(locations.map(loc => [loc.lat, loc.lng]));
    map.fitBounds(bounds, { padding: [50, 50] });

    // Mobile menu toggle
    document.getElementById('mobile-menu').addEventListener('click', function() {
      const navbarLinks = document.getElementById('navbar-links');
      navbarLinks.classList.toggle('active');
    });

    // Dropdown toggle for mobile
    const dropdowns = document.querySelectorAll('.dropdown');
    dropdowns.forEach(dropdown => {
      dropdown.addEventListener('click', function(e) {
        if (window.innerWidth <= 768) {
          e.preventDefault();
          this.classList.toggle('active');
        }
      });
    });
  </script>
</body>
</html>