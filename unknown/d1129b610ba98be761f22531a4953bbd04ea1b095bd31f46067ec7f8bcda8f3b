// Navbar Functionality
document.addEventListener('DOMContentLoaded', () => {
  // Desktop Dropdown Functionality
  function toggleDropdown(dropdownId) {
    const dropdown = document.getElementById(dropdownId + '-dropdown');
    const arrow = document.getElementById(dropdownId + '-arrow');

    if (!dropdown || !arrow) return;

    // Close all other dropdowns
    document.querySelectorAll('.dropdown-content, .media-dropdown-content').forEach(dd => {
      if (dd !== dropdown) {
        dd.classList.remove('show');
        dd.classList.add('hidden');
      }
    });

    // Toggle current dropdown
    if (dropdown.classList.contains('hidden')) {
      dropdown.classList.remove('hidden');
      dropdown.classList.add('show');
      arrow.style.transform = 'rotate(180deg)';
    } else {
      dropdown.classList.remove('show');
      dropdown.classList.add('hidden');
      arrow.style.transform = 'rotate(0deg)';
    }
  }

  // Brand Filtering
  function filterByBrand(brand) {
    // Update active brand
    document.querySelectorAll('.brand-item').forEach(item => {
      item.classList.toggle('active', item.dataset.brand === brand);
    });

    // Show/hide motorcycle sections
    document.querySelectorAll('.motorcycle-section').forEach(section => {
      section.classList.toggle('active', section.dataset.brand === brand);
    });

    // Reset category filter to 'All'
    filterByCategory('All');
  }

  // Category Filtering
  function filterByCategory(category) {
    // Update active category button
    document.querySelectorAll('.category-btn').forEach(btn => {
      btn.classList.toggle('active', btn.dataset.category === category);
    });

    // Show/hide category sections
    document.querySelectorAll('.category-section').forEach(section => {
      if (category === 'All') {
        section.style.display = 'block';
      } else {
        section.style.display = section.dataset.category === category ? 'block' : 'none';
      }
    });
  }

  // Mobile Menu Functionality
  const mobileMenu = {
    menu: document.getElementById('mobile-menu'),
    bikesDropdown: document.getElementById('mobile-bikes-dropdown'),
    menuBtn: document.getElementById('mobile-menu-btn'),
    bikesBtn: document.getElementById('mobile-bikes-btn'),
    closeBtns: document.querySelectorAll('.close-btn'),
    backBtns: document.querySelectorAll('.back-btn'),

    init() {
      // Mobile menu toggle
      this.menuBtn?.addEventListener('click', () => this.toggleMenu());
      this.bikesBtn?.addEventListener('click', () => this.toggleBikesDropdown());

      // Close buttons
      this.closeBtns.forEach(btn => {
        btn.addEventListener('click', () => {
          this.menu.classList.add('translate-x-full');
          this.bikesDropdown.classList.add('translate-x-full');
        });
      });

      // Back buttons
      this.backBtns.forEach(btn => {
        btn.addEventListener('click', () => {
          this.bikesDropdown.classList.add('translate-x-full');
        });
      });

      // Brand items in mobile view
      document.querySelectorAll('.mobile-brand-item').forEach(item => {
        item.addEventListener('click', () => {
          const brand = item.querySelector('span').textContent;
          filterByBrand(brand);
          this.bikesDropdown.classList.add('translate-x-full');
        });
      });
    },

    toggleMenu() {
      this.menu.classList.toggle('translate-x-full');
      if (!this.menu.classList.contains('translate-x-full')) {
        this.bikesDropdown.classList.add('translate-x-full');
      }
    },

    toggleBikesDropdown() {
      this.bikesDropdown.classList.toggle('translate-x-full');
      if (!this.bikesDropdown.classList.contains('translate-x-full')) {
        this.menu.classList.add('translate-x-full');
      }
    }
  };

  // Initialize Event Listeners
  function initEventListeners() {
    // Desktop dropdowns
    document.querySelectorAll('.dropdown button').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const dropdownId = e.target.closest('.dropdown').querySelector('button').textContent.trim().toLowerCase();
        toggleDropdown(dropdownId);
      });
    });

    // Brand filtering
    document.querySelectorAll('.brand-item').forEach(item => {
      item.addEventListener('click', (e) => {
        e.preventDefault();
        filterByBrand(item.dataset.brand);
      });
    });

    // Category filtering
    document.querySelectorAll('.category-btn').forEach(btn => {
      btn.addEventListener('click', () => {
        filterByCategory(btn.dataset.category);
      });
    });

    // Initialize mobile menu
    mobileMenu.init();
  }

  // Initialize everything
  initEventListeners();
}); 