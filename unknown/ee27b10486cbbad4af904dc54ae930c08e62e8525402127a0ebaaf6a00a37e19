<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Golchha Group - Motorcycles</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Custom styles */
        .header-overlay {
            background: linear-gradient(to bottom, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.5) 50%, transparent 100%);
        }

        .floating-navbar {
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            border-radius: 8px;
        }

        .top-bar-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .top-bar-left,
        .top-bar-right {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .top-bar-logo {
            height: 30px;
        }

        .top-bar-text {
            color: white;
            font-size: 14px;
            font-weight: 500;
        }

        .top-bar-icon {
            height: 16px;
            width: 16px;
        }

        .dropdown-content {
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
            border-radius: 8px;
        }

        .brand-item.active,
        .category-btn.active {
            background-color: #2563eb;
            color: white;
        }

        .category-btn {
            background-color: #f3f4f6;
            color: #4b5563;
            transition: all 0.2s;
        }

        .category-btn:hover:not(.active) {
            background-color: #e5e7eb;
        }

        .mobile-menu-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 20px;
            border-bottom: 1px solid #e5e7eb;
        }

        .motorcycle-card {
            transition: transform 0.2s;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
        }

        .motorcycle-card:hover {
            transform: translateY(-5px);
            background-color: #f9fafb;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }

        .section-title {
            font-weight: 500;
            color: #4b5563;
            margin-bottom: 12px;
            padding-top: 16px;
            border-top: 1px solid #e5e7eb;
            padding-left: 8px;
        }

        .section-title:first-child {
            border-top: none;
            padding-top: 0;
        }
    </style>
</head>

<body class="font-roboto bg-gray-50">
    <!-- Header -->
    <header class="header-overlay">
        <!-- Top Bar -->
        <div class="bg-transparent py-2 top-bar">
            <div class="top-bar-content">
                <div class="top-bar-left">
                    <img src="https://via.placeholder.com/150x40?text=Golchha+Logo" alt="golcha_logo"
                        class="top-bar-logo" />
                    <span class="top-bar-text">GOLCHHA GROUP WITH LEGACY OF 100 YEAR</span>
                </div>
                <div class="top-bar-right">
                    <svg class="top-bar-icon" viewBox="0 0 23 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M6.89761 15.1618C8.28247 16.3099 10.0607 17 12.0001 17C16.4184 17 20.0001 13.4183 20.0001 9C20.0001 8.43095 19.9407 7.87578 19.8278 7.34036M6.89761 15.1618C5.12756 13.6944 4.00014 11.4789 4.00014 9C4.00014 4.58172 7.58186 1 12.0001 1C15.8494 1 19.0637 3.71853 19.8278 7.34036M6.89761 15.1618C8.85314 14.7147 11.1796 13.7828 13.526 12.4281C16.2564 10.8517 18.4773 9.01248 19.8278 7.34036M6.89761 15.1618C4.46844 15.7171 2.61159 15.5243 1.99965 14.4644C1.36934 13.3726 2.19631 11.5969 3.99999 9.70898M19.8278 7.34036C21.0796 5.79041 21.5836 4.38405 21.0522 3.46374C20.5134 2.53051 19.0095 2.26939 16.9997 2.59929"
                            stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                    </svg>
                    <span class="top-bar-text">International website</span>
                </div>
            </div>
        </div>

        <!-- Main Navigation -->
        <div class="bg-transparent py-2 font-roboto lg:px-[150px] px-4">
            <nav class="floating-navbar bg-white my-4 px-6">
                <!-- Desktop Navigation -->
                <div class="hidden lg:flex justify-evenly items-center py-4 text-base">
                    <!-- Left Navigation Items -->
                    <div class="flex items-center space-x-8">
                        <!-- Motorcycles Dropdown -->
                        <div class="relative dropdown">
                            <button
                                class="text-sm flex items-center space-x-1 text-gray-700 hover:text-blue-600 transition-colors duration-200"
                                id="motorcycles-dropdown-btn">
                                <span>MOTORCYCLES</span>
                                <svg class="w-4 h-4 transition-transform duration-200" id="motorcycles-arrow"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>

                            <!-- Dropdown Content -->
                            <div id="motorcycles-dropdown"
                                class="dropdown-content absolute top-full left-0 mt-2 bg-white border border-gray-200 z-50 overflow-hidden hidden"
                                style="width: 1000px; height: 600px">
                                <div class="flex h-full">
                                    <!-- Left Sidebar - Brands -->
                                    <div class="w-64 bg-gray-50 p-6 rounded-l-lg flex-shrink-0">
                                        <h3 class="text-gray-800 font-semibold mb-4">BRANDS</h3>
                                        <ul class="space-y-2" id="brand-list">
                                            <!-- Brand items will be generated dynamically by JavaScript -->
                                        </ul>
                                    </div>

                                    <!-- Right Content - Motorcycles -->
                                    <div class="flex-1 flex flex-col h-full">
                                        <!-- Category Filter -->
                                        <div class="flex space-x-4 p-6 pb-4 flex-shrink-0 border-b border-gray-100"
                                            id="category-buttons-container">
                                            <!-- Category buttons will be generated dynamically by JavaScript -->
                                        </div>

                                        <!-- Motorcycle Grid -->
                                        <div id="motorcycle-grid" class="flex-1 overflow-y-auto px-6 py-4">
                                            <!-- Motorcycle sections will be generated dynamically by JavaScript -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <a href="#"
                            class="text-gray-700 text-sm hover:text-blue-600 transition-colors duration-200">SHOWROOMS</a>
                        <a href="#"
                            class="text-gray-700 text-sm hover:text-blue-600 transition-colors duration-200">WORKSHOPS</a>
                        <a href="#"
                            class="text-gray-700 text-sm hover:text-blue-600 transition-colors duration-200">EVENTS</a>
                    </div>

                    <!-- Center Logo -->
                    <img class="h-[72px] px-4" src="https://via.placeholder.com/200x60?text=Logo" alt="logo" />

                    <!-- Right Navigation Items -->
                    <div class="flex text-sm items-center space-x-8">
                        <a href="#" class="text-gray-700 hover:text-blue-600 transition-colors duration-200">BOOK TEST
                            RIDE</a>
                        <a href="#" class="text-gray-700 hover:text-blue-600 transition-colors duration-200">ABOUT
                            US</a>
                        <a href="#" class="text-gray-700 hover:text-blue-600 transition-colors duration-200">NEWS</a>

                        <!-- Media Center Dropdown -->
                        <div class="relative dropdown">
                            <button
                                class="flex items-center space-x-1 text-gray-700 hover:text-blue-600 transition-colors duration-200"
                                id="media-dropdown-btn">
                                <span>MEDIA CENTER</span>
                                <svg class="w-4 h-4 transition-transform duration-200" id="media-arrow" fill="none"
                                    stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>

                            <!-- Media Dropdown Content -->
                            <div id="media-dropdown"
                                class="media-dropdown-content absolute top-full right-0 mt-2 bg-white border border-gray-200 z-50 hidden"
                                style="width: 220px">
                                <div class="py-2">
                                    <a href="#"
                                        class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-blue-600 transition-colors duration-200">ABOUT
                                        US</a>
                                    <a href="#"
                                        class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-blue-600 transition-colors duration-200">ANNOUNCEMENTS</a>
                                    <a href="#"
                                        class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-blue-600 transition-colors duration-200">EVENTS</a>
                                    <a href="#"
                                        class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-blue-600 transition-colors duration-200">BLOGS</a>
                                    <a href="#"
                                        class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-blue-600 transition-colors duration-200">DOWNLOAD
                                        CENTER</a>
                                    <a href="#"
                                        class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-blue-600 transition-colors duration-200">CONTACT
                                        US</a>
                                    <a href="#"
                                        class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-blue-600 transition-colors duration-200">FAQS</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </nav>
        </div>
    </header>

    <!-- Page Content -->
    <div class="max-w-7xl mx-auto px-4 py-8">
        <div class="bg-white rounded-lg shadow-md p-8">
            <h1 class="text-3xl font-bold text-gray-800 mb-6">Motorcycle Showcase</h1>
            <p class="text-gray-600 mb-8">Explore our premium collection of motorcycles. Select "MOTORCYCLES" in the
                navigation menu to see our full range.</p>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="bg-gray-50 rounded-lg p-6">
                    <h2 class="text-xl font-semibold text-gray-800 mb-4">PULSAR Series</h2>
                    <p class="text-gray-600 mb-4">High-performance bikes with cutting-edge technology.</p>
                    <ul class="space-y-2">
                        <li class="flex items-center">
                            <svg class="w-5 h-5 text-blue-500 mr-2" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span>PULSAR N250</span>
                        </li>
                        <li class="flex items-center">
                            <svg class="w-5 h-5 text-blue-500 mr-2" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span>PULSAR NS200</span>
                        </li>
                        <li class="flex items-center">
                            <svg class="w-5 h-5 text-blue-500 mr-2" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span>PULSAR 150</span>
                        </li>
                    </ul>
                </div>

                <div class="bg-gray-50 rounded-lg p-6">
                    <h2 class="text-xl font-semibold text-gray-800 mb-4">DOMINAR Series</h2>
                    <p class="text-gray-600 mb-4">Powerful touring bikes for adventure seekers.</p>
                    <ul class="space-y-2">
                        <li class="flex items-center">
                            <svg class="w-5 h-5 text-blue-500 mr-2" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span>DOMINAR 400</span>
                        </li>
                        <li class="flex items-center">
                            <svg class="w-5 h-5 text-blue-500 mr-2" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span>DOMINAR 250</span>
                        </li>
                    </ul>
                </div>

                <div class="bg-gray-50 rounded-lg p-6">
                    <h2 class="text-xl font-semibold text-gray-800 mb-4">AVENGERS Series</h2>
                    <p class="text-gray-600 mb-4">Cruiser bikes for comfortable long rides.</p>
                    <ul class="space-y-2">
                        <li class="flex items-center">
                            <svg class="w-5 h-5 text-blue-500 mr-2" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span>Avenger Cruise 220</span>
                        </li>
                        <li class="flex items-center">
                            <svg class="w-5 h-5 text-blue-500 mr-2" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span>Avenger Street 160</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Bike data for the dropdowns
        const bikeData = {
            PULSAR: {
                bikes: [
                    { name: "PULSAR 220F ABS", category: "Classic", image: "https://via.placeholder.com/150x100?text=PULSAR+220F+ABS" },
                    { name: "PULSAR 150 TD", category: "Classic", image: "https://via.placeholder.com/150x100?text=PULSAR+150+TD" },
                    { name: "PULSAR 150", category: "Classic", image: "https://via.placeholder.com/150x100?text=PULSAR+150" },
                    { name: "PULSAR 125", category: "Classic", image: "https://via.placeholder.com/150x100?text=PULSAR+125" },
                    { name: "PULSAR N250", category: "N", image: "https://via.placeholder.com/150x100?text=PULSAR+N250" },
                    { name: "PULSAR NS 200 ABS FI", category: "NS", image: "https://via.placeholder.com/150x100?text=PULSAR+NS+200+ABS+FI" },
                    { name: "PULSAR NS 200 ABS", category: "NS", image: "https://via.placeholder.com/150x100?text=PULSAR+NS+200+ABS" },
                    { name: "PULSAR NS 200", category: "NS", image: "https://via.placeholder.com/150x100?text=PULSAR+NS+200" },
                    { name: "PULSAR NS 100 ABS", category: "NS", image: "https://via.placeholder.com/150x100?text=PULSAR+NS+100+ABS" },
                    { name: "NS 160 FI DUAL ABS BS6", category: "NS", image: "https://via.placeholder.com/150x100?text=NS+160+FI+DUAL+ABS+BS6" },
                    { name: "PULSAR NS 125 BS6", category: "NS", image: "https://via.placeholder.com/150x100?text=PULSAR+NS+125+BS6" },
                    { name: "PULSAR NS 125 FI BS6", category: "NS", image: "https://via.placeholder.com/150x100?text=PULSAR+NS+125+FI+BS6" }
                ]
            },
            DOMINAR: {
                bikes: [
                    { name: "DOMINAR 400", category: "Adventure", image: "https://via.placeholder.com/150x100?text=DOMINAR+400" },
                    { name: "DOMINAR 250", category: "Adventure", image: "https://via.placeholder.com/150x100?text=DOMINAR+250" }
                ]
            },
            AVENGERS: {
                bikes: [
                    { name: "Avenger Cruise 220", category: "Cruiser", image: "https://via.placeholder.com/150x100?text=Avenger+Cruise+220" },
                    { name: "Avenger Street 160", category: "Cruiser", image: "https://via.placeholder.com/150x100?text=Avenger+Street+160" }
                ]
            },
            DISCOVER: {
                bikes: [
                    { name: "DISCOVER 125", category: "Commuter", image: "https://via.placeholder.com/150x100?text=DISCOVER+125" },
                    { name: "DISCOVER 110", category: "Commuter", image: "https://via.placeholder.com/150x100?text=DISCOVER+110" }
                ]
            },
            PLATINA: {
                bikes: [
                    { name: "PLATINA 110", category: "Economy", image: "https://via.placeholder.com/150x100?text=PLATINA+110" },
                    { name: "PLATINA 100", category: "Economy", image: "https://via.placeholder.com/150x100?text=PLATINA+100" }
                ]
            }
        };

        document.addEventListener("DOMContentLoaded", () => {
            // Brand-Category mapping
            const brandCategories = {
                PULSAR: ["All", "Classic", "NS", "N"],
                DOMINAR: ["All", "Adventure"],
                AVENGERS: ["All", "Cruiser"],
                DISCOVER: ["All", "Commuter"],
                PLATINA: ["All", "Economy"],
            };

            // Desktop Dropdown Functionality
            function toggleDropdown(dropdownId) {
                const dropdown = document.getElementById(dropdownId + "-dropdown");
                const arrow = document.getElementById(dropdownId + "-arrow");

                if (!dropdown || !arrow) return;

                // Close all other dropdowns
                document
                    .querySelectorAll(".dropdown-content, .media-dropdown-content")
                    .forEach((dd) => {
                        if (dd !== dropdown) {
                            dd.classList.remove("show");
                            dd.classList.add("hidden");
                            // Reset arrow rotation for other dropdowns
                            const otherArrow = dd.parentElement.querySelector("svg");
                            if (otherArrow) {
                                otherArrow.style.transform = "rotate(0deg)";
                            }
                        }
                    });

                // Toggle current dropdown
                if (dropdown.classList.contains("hidden")) {
                    dropdown.classList.remove("hidden");
                    dropdown.classList.add("show");
                    arrow.style.transform = "rotate(180deg)";
                } else {
                    dropdown.classList.remove("show");
                    dropdown.classList.add("hidden");
                    arrow.style.transform = "rotate(0deg)";
                }
            }

            // Brand Filtering with Category Update
            function filterByBrand(brand) {
                // Update active brand
                document.querySelectorAll(".brand-item").forEach((item) => {
                    item.classList.toggle("active", item.dataset.brand === brand);
                });

                // Show/hide motorcycle sections
                document
                    .querySelectorAll(".motorcycle-section")
                    .forEach((section) => {
                        section.classList.toggle(
                            "active",
                            section.dataset.brand === brand
                        );
                    });

                // Update category buttons based on selected brand
                updateCategoryButtons(brand);

                // Reset category filter to 'All'
                filterByCategory("All");
            }

            // Update category buttons based on selected brand
            function updateCategoryButtons(brand) {
                const availableCategories = brandCategories[brand] || ["All"];

                document.querySelectorAll(".category-btn").forEach((btn) => {
                    const category = btn.dataset.category;
                    if (availableCategories.includes(category)) {
                        btn.style.display = "block";
                        btn.disabled = false;
                    } else {
                        btn.style.display = "none";
                        btn.disabled = true;
                    }
                });
            }

            // Category Filtering
            function filterByCategory(category) {
                // Update active category button
                document.querySelectorAll(".category-btn").forEach((btn) => {
                    btn.classList.toggle("active", btn.dataset.category === category);
                });

                // Show/hide category sections
                document.querySelectorAll(".category-section").forEach((section) => {
                    if (category === "All") {
                        section.style.display = "block";
                    } else {
                        section.style.display =
                            section.dataset.category === category ? "block" : "none";
                    }
                });
            }

            // Close dropdowns when clicking outside
            function closeDropdownsOnOutsideClick(event) {
                const isDropdownClick = event.target.closest(".dropdown");
                const isDropdownContent = event.target.closest(
                    ".dropdown-content, .media-dropdown-content"
                );

                if (!isDropdownClick && !isDropdownContent) {
                    // Close all dropdowns
                    document
                        .querySelectorAll(".dropdown-content, .media-dropdown-content")
                        .forEach((dd) => {
                            dd.classList.remove("show");
                            dd.classList.add("hidden");
                        });

                    // Reset all arrows
                    document.querySelectorAll(".dropdown svg").forEach((arrow) => {
                        arrow.style.transform = "rotate(0deg)";
                    });
                }
            }

            // Initialize Event Listeners
            function initEventListeners() {
                // Desktop dropdowns
                document
                    .getElementById("motorcycles-dropdown-btn")
                    ?.addEventListener("click", (e) => {
                        e.preventDefault();
                        toggleDropdown("motorcycles");
                    });

                document
                    .getElementById("media-dropdown-btn")
                    ?.addEventListener("click", (e) => {
                        e.preventDefault();
                        toggleDropdown("media");
                    });

                // Click outside to close dropdowns
                document.addEventListener("click", closeDropdownsOnOutsideClick);

                // Initialize with PULSAR as default active brand
                filterByBrand("PULSAR");
            }

            // Function to generate navbar brand list dynamically
            function generateNavbarBrandList() {
                const brandList = document.getElementById("brand-list");
                if (!brandList) return;

                brandList.innerHTML = "";
                const brands = Object.keys(bikeData);

                brands.forEach((brand, index) => {
                    const li = document.createElement("li");
                    const a = document.createElement("a");
                    a.href = "#";
                    a.className = `brand-item ${index === 0 ? "active" : ""} block px-3 py-2 text-sm text-gray-700 rounded-md transition-colors duration-200`;
                    a.dataset.brand = brand;
                    a.textContent = brand;

                    a.addEventListener("click", (e) => {
                        e.preventDefault();
                        filterByBrand(brand);
                    });

                    li.appendChild(a);
                    brandList.appendChild(li);
                });
            }

            // Function to generate category buttons dynamically
            function generateCategoryButtons() {
                const categoryContainer = document.getElementById("category-buttons-container");
                if (!categoryContainer) return;

                categoryContainer.innerHTML = "";

                // Add "All" button first
                const allBtn = document.createElement("button");
                allBtn.className = "category-btn active px-4 py-2 rounded-full text-sm font-medium transition-all duration-200";
                allBtn.dataset.category = "All";
                allBtn.textContent = "All";
                allBtn.addEventListener("click", () => filterByCategory("All"));
                categoryContainer.appendChild(allBtn);

                // Get unique categories from all bikes
                const allCategories = new Set();
                Object.values(bikeData).forEach(brandData => {
                    brandData.bikes.forEach(bike => {
                        if (bike.category) {
                            allCategories.add(bike.category);
                        }
                    });
                });

                // Add category buttons
                allCategories.forEach(category => {
                    const btn = document.createElement("button");
                    btn.className = "category-btn px-4 py-2 rounded-full text-sm font-medium transition-all duration-200";
                    btn.dataset.category = category;
                    btn.textContent = category;
                    btn.addEventListener("click", () => filterByCategory(category));
                    categoryContainer.appendChild(btn);
                });
            }

            // Function to generate motorcycle grid dynamically
            function generateMotorcycleGrid() {
                const motorcycleGrid = document.getElementById("motorcycle-grid");
                if (!motorcycleGrid) return;

                motorcycleGrid.innerHTML = "";
                const brands = Object.keys(bikeData);

                brands.forEach((brand, brandIndex) => {
                    const brandData = bikeData[brand];
                    const brandSection = document.createElement("div");
                    brandSection.className = `motorcycle-section ${brandIndex === 0 ? "active" : ""}`;
                    brandSection.dataset.brand = brand;

                    // Group bikes by category
                    const bikesByCategory = {};
                    brandData.bikes.forEach(bike => {
                        if (!bikesByCategory[bike.category]) {
                            bikesByCategory[bike.category] = [];
                        }
                        bikesByCategory[bike.category].push(bike);
                    });

                    // Create category sections
                    Object.entries(bikesByCategory).forEach(([category, bikes]) => {
                        const categorySection = document.createElement("div");
                        categorySection.className = "category-section";
                        categorySection.dataset.category = category;

                        // Add category title
                        const title = document.createElement("h3");
                        title.className = "section-title";
                        title.textContent = category;
                        categorySection.appendChild(title);

                        const grid = document.createElement("div");
                        grid.className = "grid grid-cols-4 gap-6";

                        bikes.forEach(bike => {
                            const bikeCard = document.createElement("div");
                            bikeCard.className = "motorcycle-card";

                            const img = document.createElement("img");
                            img.src = bike.image;
                            img.alt = bike.name;
                            img.className = "w-full h-32 object-contain";

                            const h4 = document.createElement("h4");
                            h4.className = "text-sm font-medium mt-2 text-center";
                            h4.textContent = bike.name;

                            bikeCard.appendChild(img);
                            bikeCard.appendChild(h4);
                            grid.appendChild(bikeCard);
                        });

                        categorySection.appendChild(grid);
                        brandSection.appendChild(categorySection);
                    });

                    motorcycleGrid.appendChild(brandSection);
                });
            }

            // Initialize everything
            initEventListeners();
            generateNavbarBrandList();
            generateCategoryButtons();
            generateMotorcycleGrid();
        });
    </script>
</body>

</html>