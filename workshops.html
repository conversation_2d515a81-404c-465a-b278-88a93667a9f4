<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Bajaj Motors - Workshops</title>
    <link
      href="https://fonts.googleapis.com/css2?family=Roboto&display=swap"
      rel="stylesheet"
    />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Roboto+Flex:opsz,wght@8..144,100..1000&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
    />
    <!-- Leaflet CSS & JS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <link rel="stylesheet" href="css/styles.css" />
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: "#3b82f6",
              secondary: "#64748b",
              accent: "#326AD2",
              success: "#10b981",
              failure: "#ef4444",
              "bajaj-red": "#E31937",
              "active-text": "#222222",
            },
            fontFamily: {
              roboto: ["Roboto", "sans-serif"],
              robotoFlex: ["Roboto Flex", "sans-serif"],
            },
          },
        },
      };
    </script>
    <style>
      #map {
        width: 100%;
        height: 100vh; /* Full viewport height */
        margin-top: 0; /* No margin - map starts from top */
      }
      
      /* Custom marker styles */
      .workshop-marker {
        background-color: #E31937;
        border: 2px solid white;
        border-radius: 50%;
        width: 20px;
        height: 20px;
      }
      
      /* Popup styles */
      .leaflet-popup-content-wrapper {
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      }
      
      .leaflet-popup-content {
        margin: 12px 16px;
        line-height: 1.4;
      }
      
      .workshop-popup h3 {
        color: #E31937;
        font-weight: bold;
        margin-bottom: 8px;
        font-size: 16px;
      }
      
      .workshop-popup p {
        margin: 4px 0;
        font-size: 14px;
        color: #333;
      }
      
      .workshop-popup .contact-info {
        margin-top: 8px;
        padding-top: 8px;
        border-top: 1px solid #eee;
      }
    </style>
  </head>

  <body class="bg-gray-50 font-roboto">
    <!-- Header -->
    <header class="fixed top-0 left-0 right-0 z-[1000]">
      <!-- Top Bar -->
      <div class="py-2 top-bar top-bar-transparent">
        <div class="top-bar-content">
          <div class="top-bar-left">
            <img
              src="assets/golcha-logo.png"
              alt="golcha_logo"
              class="top-bar-logo"
            />
            <span class="top-bar-text"
              >GOLCHHA GROUP WITH LEGACY OF 100 YEAR</span
            >
          </div>
          <div class="top-bar-right">
            <img src="./assets/globe.png" alt="globe" class="top-bar-icon" />
            <span class="top-bar-text">International website</span>
          </div>
        </div>
      </div>

      <!-- Main Navbar -->
      <div class="nav-bg py-2 font-roboto lg:px-[150px] px-4">
        <nav class="floating-navbar bg-white my-4 relative">
          <div class="px-6">
          <div class="flex justify-between items-center h-[110px]">
            <!-- Mobile Menu Button -->
            <button id="mobile-menu-btn" class="lg:hidden p-2">
              <i class="fas fa-bars text-xl text-gray-700"></i>
            </button>

            <!-- Desktop Navigation - Left Side -->
            <div class="hidden lg:flex items-center space-x-8">
              <!-- Motorcycles Dropdown -->
              <div class="relative group">
                <button class="flex items-center space-x-1 hover:text-accent">
                  <span>MOTORCYCLES</span>
                  <i class="fas fa-chevron-down text-xs"></i>
                </button>

                <!-- Mega Dropdown - Fixed Height Container -->
                <div
                  class="absolute top-full left-0 w-screen max-w-5xl bg-white shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50 mega-menu-container"
                >
                  <div class="flex h-full">
                    <!-- Categories Sidebar - Scrollable -->
                    <div class="w-64 bg-gray-100 p-4 categories-sidebar">
                      <div class="space-y-2">
                        <button
                          class="category-btn w-full text-center px-3 py-2 text-sm font-medium hover:text-accent active"
                          data-category="pulsar"
                        >
                          PULSAR
                        </button>
                        <button
                          class="category-btn w-full text-center px-3 py-2 text-sm font-medium hover:text-accent"
                          data-category="dominar"
                        >
                          DOMINAR
                        </button>
                        <button
                          class="category-btn w-full text-center px-3 py-2 text-sm font-medium hover:text-accent"
                          data-category="avengers"
                        >
                          AVENGERS
                        </button>
                        <button
                          class="category-btn w-full text-center px-3 py-2 text-sm font-medium hover:text-accent"
                          data-category="discover"
                        >
                          DISCOVER
                        </button>
                        <button
                          class="category-btn w-full text-center px-3 py-2 text-sm font-medium hover:text-accent"
                          data-category="platina"
                        >
                          PLATINA
                        </button>
                      </div>
                    </div>

                    <!-- Models Section - Scrollable -->
                    <div class="mega-menu-scrollable flex-1 flex flex-col">
                      <div class="p-6">
                        <!-- Category Tabs -->
                        <div
                          id="tabs-container"
                          class="flex space-x-6 mb-4 text-sm border-b-2"
                        >
                          <!-- Tabs will be dynamically generated -->
                        </div>

                        <!-- Models Content -->
                        <div
                          id="models-content"
                          class="model-grid gap-4 models-section"
                        >
                          <!-- Models will be populated by JavaScript -->
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <a href="/showroom-map.html" class="hover:text-accent flex items-center"
                >SHOWROOMS</a
              >
              <a href="/workshops.html" class="hover:text-accent flex items-center text-accent font-semibold"
                >WORKSHOPS</a
              >
              <a href="#" class="hover:text-accent flex items-center hide-1150"
                >EVENTS</a
              >
            </div>

            <!-- Centered Logo -->
            <div
              class="flex-1 flex justify-center items-center min-w-0 flex-shrink-0"
            >
              <a
                href="/index.html"
                class="flex items-center flex-shrink-0 min-w-[120px] justify-center"
              >
                <img class="main-logo px-4" src="assets/logo.png" alt="logo" />
              </a>
            </div>

            <!-- Desktop Navigation - Right Side -->
            <div class="hidden lg:flex items-center space-x-8">
              <a
                href="/book-test-ride.html"
                class="hover:text-accent flex items-center"
                >BOOK TEST RIDE</a
              >
              <a
                href="/about.html"
                class="hover:text-accent flex items-center hide-1150"
                >ABOUT US</a
              >
              <a href="#" class="hover:text-accent flex items-center">NEWS</a>

              <!-- Media Center Dropdown - Desktop -->
              <div class="relative group">
                <button class="flex items-center space-x-1 hover:text-accent">
                  <span>MEDIA CENTER</span>
                  <i class="fas fa-chevron-down text-xs"></i>
                </button>
                <div
                  class="absolute top-full left-0 w-48 bg-white shadow-lg rounded-md opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50 media-dropdown"
                >
                  <div class="py-2">
                    <a
                      href="#"
                      class="block px-4 py-2 hover:bg-gray-100 hover:text-accent"
                      >ABOUT US</a
                    >
                    <a
                      href="/blogs.html"
                      class="block px-4 py-2 hover:bg-gray-100 hover:text-accent"
                      >ANNOUNCEMENTS</a
                    >
                    <a
                      href="/blogs.html"
                      class="block px-4 py-2 hover:bg-gray-100 hover:text-accent"
                      >EVENTS</a
                    >
                    <a
                      href="/blogs.html"
                      class="block px-4 py-2 hover:bg-gray-100 hover:text-accent"
                      >BLOGS</a
                    >
                    <a
                      href="#"
                      class="block px-4 py-2 hover:bg-gray-100 hover:text-accent"
                      >DOWNLOAD CENTER</a
                    >
                    <a
                      href="#"
                      class="block px-4 py-2 hover:bg-gray-100 hover:text-accent"
                      >CONTACT US</a
                    >
                    <a
                      href="faqs.html"
                      class="block px-4 py-2 hover:bg-gray-100 hover:text-accent"
                      >FAQS</a
                    >
                  </div>
                </div>
              </div>
            </div>

            <!-- Mobile BIKES Button -->
            <div class="lg:hidden relative">
              <button
                id="mobile-bikes-btn"
                class="text-gray-700 font-medium flex items-center space-x-1"
              >
                <span>BIKES</span>
                <i class="fas fa-chevron-down text-xs"></i>
              </button>
            </div>
          </div>
        </div>

        <!-- Mobile Menu Overlay - Fixed z-index -->
        <div
          id="mobile-overlay"
          class="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40 hidden"
        ></div>

        <!-- Mobile Menu -->
        <div
          id="mobile-menu"
          class="lg:hidden mobile-nav-section fixed top-0 left-0 w-80 h-screen bg-white z-50 transform -translate-x-full transition-transform duration-300 hidden"
        >
          <div class="p-4 bg-white">
            <button
              id="close-mobile-menu"
              class="absolute top-4 right-4 text-xl text-gray-600"
            >
              <i class="fas fa-times"></i>
            </button>

            <!-- Mobile Menu Items -->
            <div class="mt-8 space-y-4">
              <!-- Mobile Motorcycles Dropdown -->
              <div class="mobile-dropdown">
                <button
                  class="mobile-dropdown-btn flex items-center justify-between w-full text-left font-medium text-gray-700 py-2"
                >
                  <span>BIKES</span>
                  <i class="fas fa-chevron-right text-xs"></i>
                </button>
                <div class="mobile-dropdown-content hidden pl-4 space-y-2 mt-2">
                  <button
                    class="mobile-category-btn block w-full text-left py-2 text-sm"
                    data-category="pulsar"
                  >
                    PULSAR
                  </button>
                  <button
                    class="mobile-category-btn block w-full text-left py-2 text-sm"
                    data-category="dominar"
                  >
                    DOMINAR
                  </button>
                  <button
                    class="mobile-category-btn block w-full text-left py-2 text-sm"
                    data-category="avengers"
                  >
                    AVENGERS
                  </button>
                  <button
                    class="mobile-category-btn block w-full text-left py-2 text-sm"
                    data-category="discover"
                  >
                    DISCOVER
                  </button>
                  <button
                    class="mobile-category-btn block w-full text-left py-2 text-sm"
                    data-category="platina"
                  >
                    PLATINA
                  </button>
                </div>
              </div>

              <a href="/showroom-map.html" class="block py-2 font-medium text-gray-700">SHOWROOMS</a>
              <a href="/workshops.html" class="block py-2 font-medium text-accent">WORKSHOPS</a>
              <a href="#" class="block py-2 font-medium text-gray-700">EVENTS</a>
              <a href="/book-test-ride.html" class="block py-2 font-medium text-gray-700">BOOK TEST RIDE</a>
              <a href="/about.html" class="block py-2 font-medium text-gray-700">ABOUT US</a>
              <a href="#" class="block py-2 font-medium text-gray-700">NEWS</a>

              <!-- Mobile Media Center Dropdown -->
              <div class="mobile-dropdown">
                <button
                  class="mobile-dropdown-btn flex items-center justify-between w-full text-left font-medium text-gray-700 py-2"
                >
                  <span>MEDIA CENTER</span>
                  <i class="fas fa-chevron-right text-xs"></i>
                </button>
                <div class="mobile-dropdown-content hidden pl-4 space-y-2 mt-2">
                  <a href="#" class="block py-2 text-sm">ABOUT US</a>
                  <a href="/blogs.html" class="block py-2 text-sm">ANNOUNCEMENTS</a>
                  <a href="/blogs.html" class="block py-2 text-sm">EVENTS</a>
                  <a href="/blogs.html" class="block py-2 text-sm">BLOGS</a>
                  <a href="#" class="block py-2 text-sm">DOWNLOAD CENTER</a>
                  <a href="#" class="block py-2 text-sm">CONTACT US</a>
                  <a href="faqs.html" class="block py-2 text-sm">FAQS</a>
                </div>
              </div>
            </div>
          </div>
          </div>
        </nav>
      </div>
    </header>

    <!-- Map Container -->
    <div id="map"></div>

    <!-- Footer -->
    <footer class="bg-gray-50 min-h-screen flex flex-col">
      <!-- Email Signup Section -->
      <div class="flex-1 flex items-center justify-center px-4 py-12">
        <div class="max-w-md w-full">
          <div class="text-center mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-2">
              Sign up for Email
            </h2>
            <p class="text-sm text-gray-500 mb-1">
              Read our
              <a href="#" class="text-blue-500 underline">privacy policy</a>
              to learn about data processing
            </p>
            <p class="text-sm text-gray-500">
              Sign up for BAJAJ latest news and updates
            </p>
          </div>

          <form id="emailForm" class="mb-4">
            <div class="flex gap-2 mb-2">
              <input
                type="email"
                id="email"
                placeholder="YOUR EMAIL ADDRESS"
                class="flex-1 bg-white border border-gray-300 rounded-md px-4 py-3 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
              <button
                type="submit"
                class="bg-blue-500 text-white px-6 py-3 rounded-md text-sm font-medium hover:bg-blue-600"
              >
                SUBSCRIBE NOW
              </button>
            </div>
            <p class="text-xs text-gray-500 text-center">
              This site is protected by reCAPTCHA and the Google
              <a href="#" class="underline">Privacy Policy</a> and
              <a href="#" class="underline">Terms of Service</a> apply.
            </p>
          </form>
        </div>
      </div>

      <!-- Footer Section -->
      <div class="bg-[#0F0F0F] text-white py-12">
        <div class="max-w-6xl mx-auto px-4">
          <div class="text-center mb-8">
            <div class="flex justify-center items-center mb-4">
              <div class="w-12 h-12 mr-3 flex items-center justify-center">
                <img src="/assets/golcha-logo.png" alt="" />
              </div>
              <h3 class="text-xl font-semibold">
                GOLCHHA GROUP WITH LEGACY OF 100 YEAR
              </h3>
            </div>
          </div>

          <!-- Footer Links -->
          <div class="flex justify-center gap-8 text-sm mb-8">
            <a href="#" class="hover:text-gray-300">TERMS OF USE</a>
            <a href="#" class="hover:text-gray-300">PRIVACY INFORMATION</a>
            <a href="#" class="hover:text-gray-300">COOKIES INFORMATION</a>
          </div>

          <!-- Copyright -->
          <div class="text-center text-xs text-gray-400 mb-8">
            <p>
              Copyright © 2025 Bajaj Auto Ltd – A Sole Shareholder Company - A
              Company subject to the Management and Coordination
            </p>
            <p>activities of BAJAJ AUTO. All rights reserved. VAT NO.</p>
          </div>

          <!-- Bottom Section -->
          <div class="flex flex-wrap justify-between items-center gap-4">
            <!-- Bajaj Logo -->
            <div class="h-16 w-32">
              <img src="/assets/logo.png" alt="" />
            </div>

            <!-- Social Media Icons -->
            <div class="flex gap-4 text-2xl">
              <a
                href="#"
                class="text-gray-400 hover:text-white transition-transform transform hover:-translate-y-1"
              >
                <i class="fab fa-instagram"></i>
              </a>
              <a
                href="#"
                class="text-gray-400 hover:text-white transition-transform transform hover:-translate-y-1"
              >
                <i class="fab fa-facebook"></i>
              </a>
              <a
                href="#"
                class="text-gray-400 hover:text-white transition-transform transform hover:-translate-y-1"
              >
                <i class="fab fa-youtube"></i>
              </a>
              <a
                href="#"
                class="text-gray-400 hover:text-white transition-transform transform hover:-translate-y-1"
              >
                <i class="fab fa-tiktok"></i>
              </a>
              <a
                href="#"
                class="text-gray-400 hover:text-white transition-transform transform hover:-translate-y-1"
              >
                <i class="fab fa-twitter"></i>
              </a>
              <a
                href="#"
                class="text-gray-400 hover:text-white transition-transform transform hover:-translate-y-1"
              >
                <i class="fab fa-linkedin"></i>
              </a>
            </div>

            <!-- International Website -->
            <div
              class="flex items-center gap-4 text-lg font-semibold text-white"
            >
              <img class="w-8 h-8" src="/assets/globe.png" alt="" />
              <a href="#" class="hover:text-white">International website</a>
            </div>
          </div>
        </div>
      </div>
    </footer>

    <script>
      // Mobile menu functionality
      document.addEventListener('DOMContentLoaded', function() {
        const mobileMenuBtn = document.getElementById('mobile-menu-btn');
        const mobileMenu = document.getElementById('mobile-menu');
        const mobileOverlay = document.getElementById('mobile-overlay');
        const closeMobileMenu = document.getElementById('close-mobile-menu');
        const mobileDropdownBtns = document.querySelectorAll('.mobile-dropdown-btn');

        // Toggle mobile menu
        mobileMenuBtn?.addEventListener('click', function() {
          mobileMenu.classList.remove('hidden');
          mobileMenu.classList.remove('-translate-x-full');
          mobileOverlay.classList.remove('hidden');
        });

        // Close mobile menu
        function closeMobileMenuFunc() {
          mobileMenu.classList.add('-translate-x-full');
          mobileOverlay.classList.add('hidden');
          setTimeout(() => {
            mobileMenu.classList.add('hidden');
          }, 300);
        }

        closeMobileMenu?.addEventListener('click', closeMobileMenuFunc);
        mobileOverlay?.addEventListener('click', closeMobileMenuFunc);

        // Mobile dropdown toggles
        mobileDropdownBtns.forEach(btn => {
          btn.addEventListener('click', function() {
            const content = this.nextElementSibling;
            const icon = this.querySelector('i');

            content.classList.toggle('hidden');
            icon.classList.toggle('fa-chevron-right');
            icon.classList.toggle('fa-chevron-down');
          });
        });
      });

      // Email form functionality
      document
        .getElementById("emailForm")
        .addEventListener("submit", function (e) {
          e.preventDefault();
          const email = document.getElementById("email").value;
          if (email) {
            alert(
              "Thank you for subscribing! You will receive updates at: " + email
            );
            document.getElementById("email").value = "";
          }
        });

      // Initialize the map centered on Nepal
      const map = L.map('map').setView([28.3949, 84.1240], 7);

      // Add OpenStreetMap tiles
      L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
      }).addTo(map);

      // Define workshop locations in Nepal
      const workshops = [
        {
          lat: 27.7172,
          lng: 85.3240,
          name: "Bajaj Service Center - Kathmandu",
          address: "New Road, Kathmandu",
          phone: "+977-1-4123456",
          services: "Full Service, Parts, Repairs"
        },
        {
          lat: 27.6728,
          lng: 85.4298,
          name: "Bajaj Workshop - Bhaktapur",
          address: "Durbar Square Area, Bhaktapur",
          phone: "+977-1-6123456",
          services: "Service, Maintenance, Parts"
        },
        {
          lat: 27.6786,
          lng: 85.3157,
          name: "Bajaj Service - Patan",
          address: "Lagankhel, Patan",
          phone: "+977-1-5123456",
          services: "Repairs, Service, Accessories"
        },
        {
          lat: 28.2026,
          lng: 83.9856,
          name: "Bajaj Workshop - Pokhara",
          address: "Lakeside, Pokhara",
          phone: "+977-61-123456",
          services: "Full Service, Emergency Repairs"
        },
        {
          lat: 26.4525,
          lng: 87.2718,
          name: "Bajaj Service Center - Biratnagar",
          address: "Main Road, Biratnagar",
          phone: "+977-21-123456",
          services: "Service, Parts, Maintenance"
        },
        {
          lat: 28.7050,
          lng: 80.5664,
          name: "Bajaj Workshop - Dhangadi",
          address: "Hasanpur Road, Dhangadi",
          phone: "+977-91-123456",
          services: "Repairs, Service, Parts"
        },
        {
          lat: 27.5892,
          lng: 84.5066,
          name: "Bajaj Service - Chitwan",
          address: "Bharatpur, Chitwan",
          phone: "+977-56-123456",
          services: "Full Service, Accessories"
        },
        {
          lat: 28.8417,
          lng: 80.2458,
          name: "Bajaj Workshop - Mahendranagar",
          address: "Main Bazaar, Mahendranagar",
          phone: "+977-99-123456",
          services: "Service, Emergency Repairs"
        }
      ];

      // Custom icon for workshops
      const workshopIcon = L.divIcon({
        className: 'workshop-marker',
        html: '<i class="fas fa-wrench" style="color: white; font-size: 12px; margin-top: 2px;"></i>',
        iconSize: [20, 20],
        iconAnchor: [10, 10],
        popupAnchor: [0, -10]
      });

      // Add markers for workshops
      workshops.forEach(workshop => {
        const popupContent = `
          <div class="workshop-popup">
            <h3>${workshop.name}</h3>
            <p><i class="fas fa-map-marker-alt"></i> ${workshop.address}</p>
            <div class="contact-info">
              <p><i class="fas fa-phone"></i> ${workshop.phone}</p>
              <p><i class="fas fa-tools"></i> ${workshop.services}</p>
            </div>
          </div>
        `;

        L.marker([workshop.lat, workshop.lng], { icon: workshopIcon })
          .addTo(map)
          .bindPopup(popupContent);
      });

      // Fit bounds to include all markers
      const bounds = L.latLngBounds(workshops.map(workshop => [workshop.lat, workshop.lng]));
      map.fitBounds(bounds, { padding: [50, 50] });
    </script>
  </body>
</html>
